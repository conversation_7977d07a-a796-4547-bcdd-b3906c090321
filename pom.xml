<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.mmt.hotels</groupId>
  <artifactId>Hotels-ClientGateway-Schema</artifactId>
  <version>0.13.32</version>
  <packaging>jar</packaging>

  <name>Hotels-ClientGateway-Schema</name>
  <url>http://maven.apache.org</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven-compiler-plugin.version>1.8</maven-compiler-plugin.version>
  </properties>

  <build>
		<plugins>
			<plugin>
				<groupId>org.sonarsource.scanner.maven</groupId>
				<artifactId>sonar-maven-plugin</artifactId>
				<version>3.11.0.3922</version>
			</plugin>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${maven-compiler-plugin.version}</source>
					<target>${maven-compiler-plugin.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

  <dependencies>
    <dependency>
   		<groupId>javax.validation</groupId>
    	<artifactId>validation-api</artifactId>
    	<version>2.0.1.Final</version>
	</dependency>
	  <dependency>
		  <groupId>org.projectlombok</groupId>
		  <artifactId>lombok</artifactId>
		  <version>1.18.32</version>
	  </dependency>
	  <dependency>
		  <groupId>com.fasterxml.jackson.core</groupId>
		  <artifactId>jackson-databind</artifactId>
		  <version>2.9.9</version>
	  </dependency>
	  <!-- Below dependency is temporary. Has to be removed when new contract of comparator and places and persuasions gets finalised -->
	  <dependency>
            <groupId>com.makemytrip.hotels</groupId>
            <artifactId>hotels-entity-schema</artifactId>
            <version>1.22.16</version>
       </dependency>
	  <dependency>
		  <groupId>jakarta.xml.bind</groupId>
		  <artifactId>jakarta.xml.bind-api</artifactId>
		  <version>3.0.1</version>
	  </dependency>

  </dependencies>
  <distributionManagement>
		<repository>
			<id>releases</id>
			<name>MMT Internal Releases</name>
			<url>http://nexus:8081/nexus/content/repositories/releases</url>
		</repository>
		<snapshotRepository>
			<id>snapshots</id>
			<name>MMT Internal Releases</name>
			<url>http://nexus:8081/nexus/content/repositories/snapshots</url>
		</snapshotRepository>
	</distributionManagement>
</project>
