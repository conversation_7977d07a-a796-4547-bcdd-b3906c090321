package com.mmt.hotels.clientgateway.meta.response.others;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.xml.bind.annotation.*;
import lombok.Data;

import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "RoomInfo")
@JsonIgnoreProperties(ignoreUnknown=true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class RoomInfo {
	private String ratePlanCode;
	private String supplierCode;
	private String bedTypeCode;
	private Boolean nonSmoking;
	private String paymentMode;
	private Breakfast mealPlan;
	private String ratePlanName;
	private String ratePlanDesc;
	private List<String> amenities;
	private List<Inclusion> inclusion;
	private List<CancelPenalty> cancelPenalties; //Need to take a call whether to keep it or remove it.
	private List<SpecialInstruction> specialInstructions; //Need to take a call whether to keep it or remove it.
	private List<String> finePrintPolicies;
}