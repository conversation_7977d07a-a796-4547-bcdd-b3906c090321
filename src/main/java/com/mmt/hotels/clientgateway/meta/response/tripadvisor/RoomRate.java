package com.mmt.hotels.clientgateway.meta.response.tripadvisor;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown=true)
@JsonPropertyOrder({
        "persistent_room_rate_code",
        "room_type_key",
        "rate_plan_key",
        "url",
        "line_items"
})
public class RoomRate {

    @JsonProperty("persistent_room_rate_code")
    private String persistentRoomRateCode;

    @JsonProperty("room_type_key")
    private String roomTypeKey;

    @JsonProperty("rate_plan_key")
    private String ratePlanKey;

    @JsonProperty("url")
    private String url;

    @JsonProperty("line_items")
    private List<LineItems> lineItems = new ArrayList<LineItems>();
}
