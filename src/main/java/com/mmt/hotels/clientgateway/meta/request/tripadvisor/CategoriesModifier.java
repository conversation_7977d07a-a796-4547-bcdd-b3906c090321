package com.mmt.hotels.clientgateway.meta.request.tripadvisor;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown=true)
@JsonPropertyOrder({
        "partner_booking_data",
        "real_time_pricing",
        "multiple_room_rates",
        "photos",
        "text"
})
public class CategoriesModifier {

    @JsonProperty("partner_booking_data")
    private Boolean partnerBookingData;

    @JsonProperty("real_time_pricing")
    private Boolean realTimePricing;

    @JsonProperty("multiple_room_rates")
    private Boolean multipleRoomRates;

    @JsonProperty("photos")
    private Boolean photos;

    @JsonProperty("text")
    private Boolean text;

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();
}
