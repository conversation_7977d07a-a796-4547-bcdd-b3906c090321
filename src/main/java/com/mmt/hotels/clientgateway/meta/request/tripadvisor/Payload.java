package com.mmt.hotels.clientgateway.meta.request.tripadvisor;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown=true)
@JsonPropertyOrder({
        "categories",
        "category_modifiers"
})
public class Payload {

    @JsonProperty("categories")
    private Categories categories;

    @JsonProperty("category_modifiers")
    private CategoriesModifier categoryModifiers;

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();
}
