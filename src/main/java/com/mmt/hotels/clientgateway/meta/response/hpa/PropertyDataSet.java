package com.mmt.hotels.clientgateway.meta.response.hpa;

import jakarta.xml.bind.annotation.*;

import java.util.Set;

@XmlType(propOrder = { "property", "roomData", "packageData" })
@XmlAccessorType(XmlAccessType.NONE)
public class PropertyDataSet {

    private String property;

    private Set<RoomData> roomData;

    private Set<PackageData> packageData;

    @XmlElement(name = "Property")
    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    @XmlElement(name = "RoomData")
    public Set<RoomData> getRoomData() {
        return roomData;
    }

    public void setRoomData(Set<RoomData> roomData) {
        this.roomData = roomData;
    }

    @XmlElement(name = "PackageData")
    public Set<PackageData> getPackageData() {
        return packageData;
    }

    public void setPackageData(Set<PackageData> packageData) {
        this.packageData = packageData;
    }

    @Override
    public String toString() {
        return "PropertyDataSet [property=" + property + ", packageData=" + packageData + ", roomData=" + roomData
                + "]";
    }
}
