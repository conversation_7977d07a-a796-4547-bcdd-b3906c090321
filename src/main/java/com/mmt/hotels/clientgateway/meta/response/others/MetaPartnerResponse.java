package com.mmt.hotels.clientgateway.meta.response.others;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.meta.request.others.RequestParameters;
import com.mmt.hotels.clientgateway.meta.response.MetaBaseResponse;
import jakarta.xml.bind.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * <AUTHOR>
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "HotelSearchResponse")
@JsonIgnoreProperties(ignoreUnknown=true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@EqualsAndHashCode(callSuper = true)
public class MetaPartnerResponse extends MetaBaseResponse {
	private String responseReferenceKey;
	private RequestParameters requestParameters;
	private List<HotelRate> hotelRates;
	private PartnerErrorResponse errorResponse;
}
