package com.mmt.hotels.clientgateway.meta.request.others;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mmt.hotels.clientgateway.meta.request.MetaBaseRequest;
import jakarta.xml.bind.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "APRateRequest")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class MetaPartnerRequest extends MetaBaseRequest {
    private AuthenticationHeader authenticationHeaders;
    private ProductSearch productSearch;
}
