package com.mmt.hotels.clientgateway.meta.response.tripadvisor;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown=true)
public class RoomType {

    @JsonProperty("persistent_room_type_code")
    private String persistentRoomTypeCode;

    @JsonProperty("name")
    private String name;
}
