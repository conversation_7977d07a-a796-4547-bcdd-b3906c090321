package com.mmt.hotels.clientgateway.meta.request;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mmt.hotels.clientgateway.meta.model.Brand;
import com.mmt.hotels.clientgateway.meta.model.MetaChannel;
import com.mmt.hotels.clientgateway.meta.model.MetaSubChannel;
import com.mmt.hotels.clientgateway.meta.request.hpa.SourceCountry;
import lombok.Data;

@Data
public class MetaBaseRequest {

    @JsonIgnore
    private String metaName;

    @JsonIgnore
    private MetaSubChannel subChannel;

    @JsonIgnore
    private MetaChannel metaChannel;

    @JsonIgnore
    private Brand brand;

    @JsonIgnore
    private SourceCountry sourceCountry;
}
