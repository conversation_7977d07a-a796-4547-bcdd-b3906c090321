package com.mmt.hotels.clientgateway.meta.response.others;

import jakarta.xml.bind.annotation.*;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.io.Serializable;
import java.util.Date;

public class Promotion implements Serializable {
    private static final long serialVersionUID = -2092718431503028786L;
    private String code;
    private Date startDate;
    private Date endDate;
    private String description;
    private Boolean priceSlasher;
    private String tncLink;
    private PromotionLevel promoLevel;
    private String promoType;
    private Double promoAmount;
    private String refersTo;
    private String promotionName;
    private String promotionTag;
    private String promotionShowOn;
    private String cgstPrcnt;
    private String hgstPrcnt;

    public Promotion() {
    }

    @XmlAttribute(
            name = "promotionName",
            required = false
    )
    public String getPromotionName() {
        return this.promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    @XmlAttribute(
            name = "code",
            required = true
    )
    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @XmlAttribute(
            name = "startDate",
            required = false
    )
    @XmlJavaTypeAdapter(DateAdapter.class)
    public Date getStartDate() {
        return this.startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @XmlAttribute(
            name = "endDate",
            required = false
    )
    @XmlJavaTypeAdapter(DateAdapter.class)
    public Date getEndDate() {
        return this.endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @XmlValue
    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @XmlAttribute(
            name = "priceSlasher"
    )
    public Boolean getPriceSlasher() {
        return this.priceSlasher;
    }

    public void setPriceSlasher(Boolean priceSlasher) {
        this.priceSlasher = priceSlasher;
    }

    @XmlAttribute(
            name = "TNCLink"
    )
    public String getTncLink() {
        return this.tncLink;
    }

    public void setTncLink(String tncLink) {
        this.tncLink = tncLink;
    }

    @XmlAttribute(
            name = "promoLevel"
    )
    public PromotionLevel getPromoLevel() {
        return this.promoLevel;
    }

    public void setPromoLevel(PromotionLevel promoLevel) {
        this.promoLevel = promoLevel;
    }

    @XmlAttribute(
            name = "promoType"
    )
    public String getPromoType() {
        return this.promoType;
    }

    public void setPromoType(String promoType) {
        this.promoType = promoType;
    }

    @XmlAttribute(
            name = "amount",
            required = false
    )
    public Double getPromoAmount() {
        return this.promoAmount;
    }

    public void setPromoAmount(Double promoAmount) {
        this.promoAmount = promoAmount;
    }

    @XmlTransient
    public String getRefersTo() {
        return this.refersTo;
    }

    public void setRefersTo(String refersTo) {
        this.refersTo = refersTo;
    }

    public int hashCode() {
        HashCodeBuilder hashCodeBuilder = new HashCodeBuilder(17, 31);
        hashCodeBuilder = hashCodeBuilder.append(this.code);
        return hashCodeBuilder.toHashCode();
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        } else if (obj == null) {
            return false;
        } else if (this.getClass() != obj.getClass()) {
            return false;
        } else {
            Promotion promotion = (Promotion)obj;
            EqualsBuilder equalsBuilder = new EqualsBuilder();
            equalsBuilder = equalsBuilder.append(this.code, promotion.code);
            return equalsBuilder.isEquals();
        }
    }

    @XmlAttribute(
            name = "tag",
            required = false
    )
    public String getPromotionTag() {
        return this.promotionTag;
    }

    public void setPromotionTag(String promotionTag) {
        this.promotionTag = promotionTag;
    }

    @XmlAttribute(
            name = "promotionShowOn",
            required = false
    )
    public String getPromotionShowOn() {
        return this.promotionShowOn;
    }

    public void setPromotionShowOn(String promotionShowOn) {
        this.promotionShowOn = promotionShowOn;
    }

    @XmlAttribute(
            name = "cgstPrcnt",
            required = false
    )
    public String getCgstPrcnt() {
        return this.cgstPrcnt;
    }

    public void setCgstPrcnt(String cgstPrcnt) {
        this.cgstPrcnt = cgstPrcnt;
    }

    @XmlAttribute(
            name = "hgstPercnt",
            required = false
    )
    public String getHgstPrcnt() {
        return this.hgstPrcnt;
    }

    public void setHgstPrcnt(String hgstPrcnt) {
        this.hgstPrcnt = hgstPrcnt;
    }

    @XmlEnum
    public static enum PromotionType {
        @XmlEnumValue("default")
        PROMO_TYPE_DEFAULT("default"),
        @XmlEnumValue("info")
        PROMO_TYPE_INFO("informational"),
        @XmlEnumValue("deal")
        PROMO_TYPE_DEAL("deal"),
        @XmlEnumValue("Mark-Up")
        PROMO_TYPE_MARKUP("Mark-Up"),
        @XmlEnumValue("Discount")
        PROMO_TYPE_DISCOUNT("Discount"),
        @XmlEnumValue("ServiceTaxOnMarkup")
        PROMO_TYPE_SERVICETAX_ON_MARKUP("ServiceTaxOnMarkup"),
        @XmlEnumValue("GSTOnMarkup")
        GST_ON_MARKUP("GSTOnMarkup"),
        @XmlEnumValue("CouponGST")
        COUPON_GST("COUPON_GST"),
        @XmlEnumValue("CouponDisc")
        COUPON_DISC("COUPON_DISC"),
        @XmlEnumValue("NetRateMarkup")
        NETEATE_MARKUP("NetRateMarkup"),
        @XmlEnumValue("AffiliateFee")
        AFFILIATE_FEE("AffiliateFee"),
        @XmlEnumValue("CouponDSD")
        COUPON_DSD("COUPON_DSD");

        private String value;

        private PromotionType(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    @XmlEnum
    public static enum PromotionLevel {
        @XmlEnumValue("ROOM_LEVEL")
        ROOM_LEVEL_PROMO("roomLevel"),
        @XmlEnumValue("TARIFF_LEVEL")
        TARIFF_LEVEL_PROMO("tariffLevel"),
        @XmlEnumValue("HOTEL_LEVEL")
        HOTEL_LEVEL_PROMO("hotelLevel"),
        @XmlEnumValue("AP_LEVEL")
        AP_LEVEL_PROMO("apLevel"),
        @XmlEnumValue("MSE_LEVEL")
        MSE_LEVEL_PROMO("mseLevel"),
        @XmlEnumValue("DSD_LEVEL")
        DSD_LEVEL_PROMO("dsdLevel");

        private String value;

        private PromotionLevel(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }
}
