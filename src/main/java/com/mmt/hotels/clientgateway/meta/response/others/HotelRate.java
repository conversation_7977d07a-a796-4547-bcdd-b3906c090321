package com.mmt.hotels.clientgateway.meta.response.others;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.xml.bind.annotation.*;
import lombok.Data;

import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "HotelRate")
@JsonIgnoreProperties(ignoreUnknown=true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class HotelRate {
	private String hotelCode;
	private String hotelName;
	private String currencyCode;
	private String cheapestRoomCode;
	private HotelInfo hotelInfo;
	private List<Room> rooms;
}
