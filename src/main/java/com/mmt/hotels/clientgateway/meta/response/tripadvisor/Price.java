package com.mmt.hotels.clientgateway.meta.response.tripadvisor;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown=true)
public class Price {

    @JsonProperty("requested_currency_price")
    private RequestedCurrencyPrice requestedCurrencyPrice;
}
