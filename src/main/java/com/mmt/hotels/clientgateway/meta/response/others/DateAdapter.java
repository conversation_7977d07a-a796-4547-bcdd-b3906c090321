package com.mmt.hotels.clientgateway.meta.response.others;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateAdapter extends XmlAdapter<String, Date> {

    private static final String DATE_FORMAT_STRING = "yyyy-MM-dd";

    @Override
    public Date unmarshal(String dateString) throws Exception {
        Date date = null;
        if (dateString != null && !dateString.isEmpty()) {
            DateFormat sdf = new SimpleDateFormat(DATE_FORMAT_STRING);
            sdf.setLenient(false);
            date = sdf.parse(dateString);
        }
        return date;
    }

    @Override
    public String marshal(Date date) throws Exception {
        String dateString = null;
        if (date != null) {
            DateFormat sdf = new SimpleDateFormat(DATE_FORMAT_STRING);
            sdf.setLenient(false);
            dateString = sdf.format(date);
        }
        return dateString;
    }
}
