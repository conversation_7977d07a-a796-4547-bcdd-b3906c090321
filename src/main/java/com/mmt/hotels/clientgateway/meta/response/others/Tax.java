package com.mmt.hotels.clientgateway.meta.response.others;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.xml.bind.annotation.*;
import lombok.Data;

@XmlRootElement(name = "tax")
@XmlAccessorType(XmlAccessType.FIELD)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class Tax {
	private String value;
	private String type;
	private String amount;
	private String isPerPerson;
}