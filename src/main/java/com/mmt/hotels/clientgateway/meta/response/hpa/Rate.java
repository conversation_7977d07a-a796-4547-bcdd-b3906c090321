package com.mmt.hotels.clientgateway.meta.response.hpa;

import com.mmt.hotels.clientgateway.meta.request.hpa.OccupancyDetails;
import jakarta.xml.bind.annotation.*;
@XmlType(propOrder = { "rateRuleId", "baseRate", "tax", "otherFees", "occupancy", "occupancyDetails", "custom1",
        "custom2", "custom3", "refundable" })
@XmlAccessorType(XmlAccessType.NONE)
public class Rate {

    private String rateRuleId;

    private RateAccessor baseRate;

    private RateAccessor tax;

    private String custom1;

    private String custom2;

    private String custom3;

    private Integer occupancy;

    private OccupancyDetails occupancyDetails;

    private RateAccessor otherFees;

    private Refundable refundable;

    @XmlAttribute(name = "rate_rule_id")
    public String getRateRuleId() {
        return rateRuleId;
    }

    public void setRateRuleId(String rateRuleId) {
        this.rateRuleId = rateRuleId;
    }

    @XmlElement(name = "Baserate")
    public RateAccessor getBaseRate() {
        return baseRate;
    }

    public void setBaseRate(RateAccessor baseRate) {
        this.baseRate = baseRate;
    }

    @XmlElement(name = "Tax")
    public RateAccessor getTax() {
        return tax;
    }

    public void setTax(RateAccessor tax) {
        this.tax = tax;
    }

    @XmlElement(name = "Custom1")
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    @XmlElement(name = "Custom2")
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    @XmlElement(name = "Custom3")
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    @XmlElement(name = "Occupancy")
    public Integer getOccupancy() {
        return occupancy;
    }

    public void setOccupancy(Integer occupancy) {
        this.occupancy = occupancy;
    }

    @XmlElement(name = "OccupancyDetails")
    public OccupancyDetails getOccupancyDetails() {
        return occupancyDetails;
    }

    public void setOccupancyDetails(OccupancyDetails occupancyDetails) {
        this.occupancyDetails = occupancyDetails;
    }

    @XmlElement(name = "OtherFees")
    public RateAccessor getOtherFees() {
        return otherFees;
    }

    public void setOtherFees(RateAccessor otherFees) {
        this.otherFees = otherFees;
    }

    @XmlElement(name = "Refundable")
    public Refundable getRefundable() {
        return refundable;
    }

    public void setRefundable(Refundable refundable) {
        this.refundable = refundable;
    }

    @Override
    public String toString() {
        return "Rate{" +
                "rateRuleId='" + rateRuleId + '\'' +
                ", baseRate=" + baseRate +
                ", tax=" + tax +
                ", custom1='" + custom1 + '\'' +
                ", custom2='" + custom2 + '\'' +
                ", custom3='" + custom3 + '\'' +
                ", occupancy=" + occupancy +
                ", occupancyDetails=" + occupancyDetails +
                ", otherFees=" + otherFees +
                ", refundable=" + refundable +
                '}';
    }
}
