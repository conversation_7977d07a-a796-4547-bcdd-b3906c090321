package com.mmt.hotels.clientgateway.request;

import lombok.Data;

import java.util.List;

@Data
public class CollectionCriteria {

    private boolean trendingNow;

    private boolean collectionRequired;

    private boolean suggestedForYouCards;

    private boolean propertyTypeCards;

    private String collectionsCount;

    private List<String> collectionIds;

    private String athenaCategory;

    private boolean staticFilterCardsRequired;

    private boolean inspiredCardsRequired;

    //if valuestay card is requested in scion request we send this value as true
    private boolean valueStayCardsRequired;

    private boolean discoverByDestinationCardsRequired;

    private boolean luxeCardRequired;
    private boolean bannerListCardRequired;
    private boolean familyCardRequired;
    private boolean hostCardRequired;
    private boolean offbeatCitiesCardsRequired;

}
