package com.mmt.hotels.clientgateway.request;

import lombok.Data;

@Data
public class RequiredApis {
    private boolean metaRequired;
    private boolean personalizationRequired;
    private boolean mmrRequired;
    private boolean filterSuggestionRequired;
    private boolean mmrV2Required;
    private boolean comparatorRequired;
    private boolean reviewSummaryRequired;
    private boolean luxuryRequired;
    private boolean persuasionsRequired;
    private boolean placesRequired;
    private boolean topReviewsRequired;
    private boolean detailPersuasionCardsRequired;
	private boolean weaverResponseRequired;
	private boolean roomInfoRequired;
	private boolean comparatorV2Required;
    private boolean giLandingCardInfoRequired;
    private boolean myPartnerHeroLoyalty;
    private boolean pagemakerRequired;
    private boolean smartEngageRequired;
    private boolean cardRequired;
}
