package com.mmt.hotels.clientgateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.hotels.model.request.payment.UserDetail;
import com.mmt.hotels.model.response.pricing.SpecialRequest;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class CreateQuoteRequest extends BaseRequest {
    private String txnKey;
    private UserDetail userDetail;
    private List<TravelerDetail> travelerDetailsList;
    private SpecialRequest specialRequest;
}
