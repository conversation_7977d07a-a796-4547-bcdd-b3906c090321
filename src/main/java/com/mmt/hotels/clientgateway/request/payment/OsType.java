package com.mmt.hotels.clientgateway.request.payment;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.Map;

public enum OsType {
    @JsonProperty("iOS")
    IOS("iOS"),
    @<PERSON><PERSON><PERSON>roperty("Android")
    AND<PERSON>ID("Android"),
    @JsonProperty("Desktop")
    DESKTOP("Desktop");

    private static Map<String, OsType> map = new HashMap<>();

    static {
        for (OsType osType : OsType.values()) {
            map.put(osType.getValue(), osType);
        }
    }

    private String value;

    OsType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, OsType> getMap() {
        return map;
    }
}
