package com.mmt.hotels.clientgateway.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SearchHotelsCriteria extends SearchCriteria{
	
	private List<String> hotelIds;
	
	@NotNull
	private Integer limit;
	
	private String lastHotelId;

	private String lastFetchedWindowInfo;
	public Boolean guestHouseAvailable;

	private String lastHotelCategory;
	
	private boolean personalizedSearch;

	private boolean nearBySearch;
	private boolean wishListedSearch;

	private Integer totalHotelsShown;

	private String sectionsType;
	
	@NotEmpty
	@Valid
	private List<RoomStayCandidate> roomStayCandidates;

	private CollectionCriteria collectionCriteria;

}
