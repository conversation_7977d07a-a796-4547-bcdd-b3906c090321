package com.mmt.hotels.clientgateway.request;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.mmt.hotels.clientgateway.request.dayuse.Slot;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;


public class SearchCriteria {

	@NotEmpty
	private String checkIn;
	
	@NotEmpty
	private String checkOut;

	private String countryCode;
	
	private String cityCode;

	private String cityName;

	private String locationId;
	
	private String locationType;

	private String userSearchType;
	
	private Double lat;
	
	private Double lng;
	
	private String currency;

	private List<String> travellerEmailId;

	private boolean isBookingForGuest;

	private boolean personalCorpBooking;

	private boolean rmDHS; //NearBy SEO Flag

	private String boostProperty; // booster propertyType for SEO Pages

	private String baseRateplanCode;
	private SelectedRatePlan selectedRatePlan;
	private MultiCurrencyInfo multiCurrencyInfo;
	private UserGlobalInfo userGlobalInfo;

	private boolean preAppliedFilter; // This represent if this is request for the preApplied Filter or not, Ideally
	// this should be true for all the first time requests coming from the clients

	public boolean isPreAppliedFilter() {
		return preAppliedFilter;
	}

	public void setPreAppliedFilter(boolean preAppliedFilter) {
		this.preAppliedFilter = preAppliedFilter;
	}

	public String getBaseRateplanCode() {
		return baseRateplanCode;
	}

	public void setBaseRateplanCode(String baseRateplanCode) {
		this.baseRateplanCode = baseRateplanCode;
	}

	public SelectedRatePlan getSelectedRatePlan() {
		return selectedRatePlan;
	}

	public void setSelectedRatePlan(SelectedRatePlan selectedRatePlan) {
		this.selectedRatePlan = selectedRatePlan;
	}

	@SerializedName("PaxDetails")
	private List<RoomStayCandidate> roomStayCandidates;

	public String getParentLocationId() {
		return parentLocationId;
	}

	public void setParentLocationId(String parentLocationId) {
		this.parentLocationId = parentLocationId;
	}

	public String getParentLocationType() {
		return parentLocationType;
	}

	public void setParentLocationType(String parentLocationType) {
		this.parentLocationType = parentLocationType;
	}

	private String parentLocationId; //parent Zone Id to build location Persuasions for hotels from Hub City
	private String parentLocationType;

	public boolean isPersonalCorpBooking() { return personalCorpBooking; }

	public void setPersonalCorpBooking(boolean personalCorpBooking) { this.personalCorpBooking = personalCorpBooking; }

	public boolean isBookingForGuest() {
		return isBookingForGuest;
	}

	public void setBookingForGuest(boolean bookingForGuest) {
		isBookingForGuest = bookingForGuest;
	}

	private String tripType;

	private Slot slot;

	private String giHotelId;
	@JsonProperty("vcId")
	@JsonAlias("vcid")
	private String vcId;

	public String getGiHotelId() {
		return giHotelId;
	}

	public void setGiHotelId(String giHotelId) {
		this.giHotelId = giHotelId;
	}

	public String getVcId() {
		return vcId;
	}

	public void setVcId(String vcId) {
		this.vcId = vcId;
	}

	public String getCheckIn() {
		return checkIn;
	}

	public void setCheckIn(String checkIn) {
		this.checkIn = checkIn;
	}

	public String getCheckOut() {
		return checkOut;
	}

	public void setCheckOut(String checkOut) {
		this.checkOut = checkOut;
	}

	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getLocationId() {
		return locationId;
	}

	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}

	public String getLocationType() {
		return locationType;
	}

	public void setLocationType(String locationType) {
		// Allow only certain location types to be set as is, others should be converted to lower case
		List<String> exceptionalLocationTypes = Arrays.asList("geoArea");

		if(exceptionalLocationTypes.contains(locationType)){
			this.locationType = locationType;
		}else{
			this.locationType =  StringUtils.isNotBlank(locationType) ? locationType.toLowerCase() : null;
		}
	}

	public Double getLat() {
		return lat;
	}

	public void setLat(Double lat) {
		this.lat = lat;
	}

	public Double getLng() {
		return lng;
	}

	public void setLng(Double lng) {
		this.lng = lng;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public List<String> getTravellerEmailID() {
		return travellerEmailId;
	}

	public void setTravellerEmailID(List<String> travellerEmailId) {
		this.travellerEmailId = travellerEmailId;
	}

	public String getTripType() {
		return tripType;
	}

	public void setTripType(String tripType) {
		this.tripType = tripType;
	}

	public Slot getSlot() {
		return slot;
	}

	public void setSlot(Slot slot) {
		this.slot = slot;
	}

	public boolean getRmDHS(){
		return rmDHS;
	}

	public void setRmDHS(){
		this.rmDHS = rmDHS;
	}

	public String getBoostProperty() {return boostProperty;}

	public void setBoostProperty(String boostProperty) {this.boostProperty = boostProperty;}

	public List<RoomStayCandidate> getRoomStayCandidates() {
		return roomStayCandidates;
	}

	public void setRoomStayCandidates(List<RoomStayCandidate> roomStayCandidates) {
		this.roomStayCandidates = roomStayCandidates;
	}
	public MultiCurrencyInfo getMultiCurrencyInfo() {
		return multiCurrencyInfo;
	}

	public void setMultiCurrencyInfo(MultiCurrencyInfo multiCurrencyInfo) {
		this.multiCurrencyInfo = multiCurrencyInfo;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public UserGlobalInfo getUserGlobalInfo() {
		return userGlobalInfo;
	}

	public void setUserGlobalInfo(UserGlobalInfo userGlobalInfo) {
		this.userGlobalInfo = userGlobalInfo;
	}

	public String getUserSearchType() {
		return userSearchType;
	}

	public void setUserSearchType(String userSearchType) {
		this.userSearchType = userSearchType;
	}
}
