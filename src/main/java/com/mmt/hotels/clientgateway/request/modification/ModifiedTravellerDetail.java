package com.mmt.hotels.clientgateway.request.modification;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;

@Data
public class ModifiedTravellerDetail {

    private int title = 1;
    @JsonProperty("master_pax")
    private boolean masterPax;
    private Salutation salutation;
    @JsonProperty("first_name")
    private String firstName;
    @JsonProperty("last_name")
    private String lastName;
    @JsonProperty("mobile_no")
    private String mobileNo;
    @JsonProperty("isd_code")
    private String isdCode;
    @JsonProperty("email_id")
    private String email;
    @JsonProperty("room_no")
    private Integer roomNo;

    @Getter
    public enum Salutation {
        MR("Mr."),
        MS("Ms."),
        MRS("Mrs.");


        private final String salutation;

        Salutation(String salutation) {
            this.salutation = salutation;
        }
    }
}
