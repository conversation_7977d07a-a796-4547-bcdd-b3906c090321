package com.mmt.hotels.clientgateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class GroupBookingRequest extends BaseSearchRequest{
    private Map<String,Field> inputFields;
    private GroupBookingSearchCriteria searchCriteria;
}
