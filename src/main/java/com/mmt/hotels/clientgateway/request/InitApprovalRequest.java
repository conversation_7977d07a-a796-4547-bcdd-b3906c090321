package com.mmt.hotels.clientgateway.request;

import com.mmt.hotels.clientgateway.request.payment.SpecialRequest;
import com.mmt.hotels.clientgateway.request.payment.TravellerDetail;
import com.mmt.hotels.clientgateway.request.payment.TripTag;
import lombok.Data;

import java.util.List;

@Data
public class InitApprovalRequest {

    private String employeeComment;
    private ReasonForTravel reasonForTravel;
    private String workflowStatus;
    private SpecialRequest specialRequest;
    private List<TravellerDetail> travellerDetailsList;
    private TripTag tripTag;
    private String txnKey;
    private String requisitionID; //unique id in case of mybiz Decentralized/DarwinBox flow
    private String myBizFlowIdentifier; //mybiz flow type identifier
    private RequestDetails requestDetails;
    private String externalChainMembershipID;  // loyalty number from client for myBiz users
}
