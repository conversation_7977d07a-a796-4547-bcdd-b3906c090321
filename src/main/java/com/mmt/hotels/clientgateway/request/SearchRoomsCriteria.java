package com.mmt.hotels.clientgateway.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

@Data
@EqualsAndHashCode(callSuper = true)
public class SearchRoomsCriteria extends SearchCriteria{

	private String hotelId;
	
	@NotEmpty
	@Valid
	private List<RoomStayCandidate> roomStayCandidates;
	
	private String mtKey;
	private Boolean guestHouseAvailable;
	private String hotelType;

}
