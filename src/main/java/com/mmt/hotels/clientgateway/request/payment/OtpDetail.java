package com.mmt.hotels.clientgateway.request.payment;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OtpDetail {

    @JsonProperty("OTP")
    private String otp;
    @JsonProperty("key")
    private String key;

    @JsonProperty("OTP")
    public String getOtp() {
        return otp;
    }
    @JsonProperty("OTP")
    public void setOtp(String otp) {
        this.otp = otp;
    }
    @JsonProperty("key")
    public String getKey() {
        return key;
    }
    @JsonProperty("key")
    public void setKey(String key) {
        this.key = key;
    }
}
