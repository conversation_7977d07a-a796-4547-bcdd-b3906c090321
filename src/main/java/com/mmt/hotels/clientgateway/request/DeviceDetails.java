package com.mmt.hotels.clientgateway.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class DeviceDetails {
	
	@NotEmpty
	private String appVersion;
	
	@NotEmpty
	private String deviceId;
	
	@NotEmpty
	private String deviceType;
	
	@NotEmpty
	private String bookingDevice;

	private String networkType;

	private String deviceName; //Model name of the device (e.g, xiaomi Redmi Note 7 Pro)

	private String appVersionIntGi;

	private List<String> simSerialNo;
}
