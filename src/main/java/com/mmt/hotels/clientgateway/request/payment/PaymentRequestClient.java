package com.mmt.hotels.clientgateway.request.payment;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helperV2.UserPaymentCardDetails;
import com.mmt.hotels.clientgateway.request.AddOnDetailState;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;


@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentRequestClient {
    private String correlationKey;
    private boolean skipRtbValidation;
    private String tripDetailsText;
    private boolean incognitoMode;
    private boolean skipInitPayment;
    private String brand;

    @NotNull
    @Valid
    private String transactionKey;
    private boolean recheckRequired;
    @NotNull
    @Valid
    private String currency;

    @NotNull
    @Valid
    private PaymentDetail paymentDetail;
    private AuthenticationDetail authenticationDetail;

    @NotNull
    @Valid
    private List<TravellerDetail> travellerDetails;
    private ErrorConfig errorConfig;
    private boolean requiredOtpValidation;
    private boolean skipDoubleBlack;
    private String fk_token;
    private SpecialRequest specialRequest;
    private boolean alternateCurrencySelected;

    @NotNull
    @Valid
    private String idContext = "B2C"; //default is B2C
    private AdditionalCheckoutInfo additionalCheckoutInfo;
    private String quoteId;

    private TripTag tripTag;
    private String workflowStatus;
    private boolean personalCorpBooking;
    private String reasonForSkipApproval;

    // this flag denotes if corp user is using quickCheckout in which the user if eligible can pay from company wallet without approval
    private boolean myBizWalletQuickPay;

    private boolean tncClauseChecked;

    // HTL-39856 client will send this flag in case of myPartner Express checkout on review page
    private boolean myPartnerExpressCheckout;

    // HTL-42824 gst details will be sent from payment checkout for logged out users and first time booking users.
    private GstnDetail gstnDetail;
    private boolean tcsCollected;

    private RequestDetails requestDetails;
    private JsonNode omnitureEventData;

    private boolean promoConsent;
    private boolean quickCheckoutApplicable;
    private String flexibleCheckinSlotId;
    private UserPaymentCardDetails paymentCardDetails;
    private Map<String, AddOnDetailState> addOnDetail;
    private JsonNode reviewPageOmni;

    private String externalChainMembershipID; // loyalty number from client for myBiz users
}
