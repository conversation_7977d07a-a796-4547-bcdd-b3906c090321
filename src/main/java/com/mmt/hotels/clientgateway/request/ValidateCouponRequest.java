package com.mmt.hotels.clientgateway.request;

import com.mmt.hotels.model.request.UserLocation;

public class ValidateCouponRequest extends BaseRequest{

	private String countryCode;
	private String txnKey;
	private String couponCode;
	private String quoteId;
	private boolean removeCoupon;
	private String expData;

	private FeatureFlags featureFlags;
	
    private UserLocation userLocation;

	private RequestDetails requestDetails;

	private boolean quickCheckoutApplicable;

	public boolean isQuickCheckoutApplicable() {
		return quickCheckoutApplicable;
	}

	public void setQuickCheckoutApplicable(boolean quickCheckoutApplicable) {
		this.quickCheckoutApplicable = quickCheckoutApplicable;
	}

	public RequestDetails getRequestDetails() {
		return requestDetails;
	}

	public void setRequestDetails(RequestDetails requestDetails) {
		this.requestDetails = requestDetails;
	}

	public String getExpData() {
		return expData;
	}

	public void setExpData(String expData) {
		this.expData = expData;
	}

	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	public String getTxnKey() {
		return txnKey;
	}

	public void setTxnKey(String txnKey) {
		this.txnKey = txnKey;
	}

	public String getCouponCode() {
		return couponCode;
	}

	public void setCouponCode(String couponCode) {
		this.couponCode = couponCode;
	}

	public boolean isRemoveCoupon() {
		return removeCoupon;
	}

	public void setRemoveCoupon(boolean removeCoupon) {
		this.removeCoupon = removeCoupon;
	}

	public String getQuoteId() {
		return quoteId;
	}

	public void setQuoteId(String quoteId) {
		this.quoteId = quoteId;
	}

	public FeatureFlags getFeatureFlags() {
		return featureFlags;
	}

	public void setFeatureFlags(FeatureFlags featureFlags) {
		this.featureFlags = featureFlags;
	}

	public UserLocation getUserLocation() {
		return userLocation;
	}

	public void setUserLocation(UserLocation userLocation) {
		this.userLocation = userLocation;
	}
}
