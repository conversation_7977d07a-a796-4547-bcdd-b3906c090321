package com.mmt.hotels.clientgateway.request;

import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;

import lombok.Data;

@Data
public class TrafficSource {
	
	@NotEmpty
	private String source;
	
	@NotEmpty
	private String type;

	/*
	 * this is nullable field
	 * this is added to differentiate between user coming from SEO to funnel page
	 */
	private String flowType;

	private String aud;
}
