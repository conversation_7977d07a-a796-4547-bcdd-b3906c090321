package com.mmt.hotels.clientgateway.request.payment;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PriceBreakUp implements Serializable {
    private static final long serialVersionUID = 6869217523961792364L;
    protected double displayPrice;
    protected double displayPriceAlternateCurrency;
    protected double nonDiscountedPrice;
    protected double savingPerc;

    public PriceBreakUp() {
    }


}
