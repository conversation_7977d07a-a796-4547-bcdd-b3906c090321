package com.mmt.hotels.clientgateway.request.ugc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown=true)
public class ClientDeviceDetails {
    private String appVersion;
    private String bookingDevice;
    private String deviceId;
    private String deviceName;
    private String deviceType;
    private String networkType;
    private String resolution;
}
