package com.mmt.hotels.clientgateway.request;


import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class UpdatedEMISearchCriteria extends SearchCriteria{
    @NotEmpty
    private String searchType;

    @NotEmpty
    private String hotelId;

    @NotEmpty
    @Valid
    private List<UpdatedEMIRoomCriteria> roomCriteria;

    @NotEmpty
    @Valid
    private List<RoomStayCandidate> roomStayCandidates;
}
