package com.mmt.hotels.clientgateway.request.ugc;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(
        ignoreUnknown = true
)
@JsonInclude(Include.NON_NULL)
public class ClientSubmitApiRequest {
    private QuestionSet question;
    private List<QuestionSet> questions;
    int pageId;
    Integer userPage;
    String ugcId;
    String bookingDevice;
    String bookingId;
    String token;
    String ver;
    String mmtAuth;
    String hotelId;
    String programId;
    String metaSrc;
    String client;
}
