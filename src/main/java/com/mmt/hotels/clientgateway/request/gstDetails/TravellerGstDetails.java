package com.mmt.hotels.clientgateway.request.gstDetails;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TravellerGstDetails {
    private String uuid;
    private Integer travellerGstId;
    private String gstOwner;
    private String gstNumber;
    private String billingAddress;
    private String companyName;
    private String state;
    private String pinCode;
    private Boolean isDefault;
}
