package com.mmt.hotels.clientgateway.request.ugc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.mmt.hotels.clientgateway.response.ugc.MediaDetails;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(
        ignoreUnknown = true
)
@JsonInclude(Include.NON_NULL)
public class QuestionSet {
    private String questionType;
    private List<String> selected;
    private Integer indexId;
    private int rating;
    private String text;
    private String questionId;
    private String title;
    private List<MediaDetails> mediaDetails;
    private boolean voiceInput;
}
