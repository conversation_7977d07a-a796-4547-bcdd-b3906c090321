package com.mmt.hotels.clientgateway.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class UpdatedPriceCriteria extends SearchCriteria {

    @NotEmpty
    private String searchType;

    @NotEmpty
    @Valid
    private List<UpdatedPriceRoomCriteria> roomCriteria;
    
    @NotEmpty
	private String hotelId;

    private String payMode;

}
