package com.mmt.hotels.clientgateway.request;

import com.mmt.hotels.clientgateway.response.EMIDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class AvailRoomsRequest extends BaseSearchRequest{

    //put @notnull inside these for request wherever is applicable
    @Valid
    private EMIDetail emiDetail;

    @NotNull
    @Valid
    private AvailPriceCriteria searchCriteria;

}
