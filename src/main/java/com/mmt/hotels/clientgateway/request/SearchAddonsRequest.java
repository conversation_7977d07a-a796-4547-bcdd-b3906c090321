package com.mmt.hotels.clientgateway.request;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SearchAddonsRequest extends BaseSearchRequest{

	@NotNull
	@Valid
	private SearchAddonsCriteria searchCriteria;
	
    private boolean netRateSelected;
    
    private double totalPriceWithTax;

    private double totalPriceWithoutTax;
}
