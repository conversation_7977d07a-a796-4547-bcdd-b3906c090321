package com.mmt.hotels.clientgateway.request.wishlist;

import com.mmt.hotels.clientgateway.request.BaseSearchRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 20-Jan-2022, Thursday 7:21 PM
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WishListedHotelsDetailRequest extends BaseSearchRequest {

    @NotNull
    @Valid
    private WishListedHotelsDetailCriteria searchCriteria;

}
