package com.mmt.hotels.clientgateway.businessobjects.affiliate.helperV2;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class UserPaymentCardDetails {

    @NotBlank(message = "CardType can not be blank")
    private String cardType; // Whether card is VISA,MASTER,JCB,DISCOVER,DINERS,DSCVR,AMEX,RUPAY,MAESTRO,VISACOBRAND,MASTERCOBRAND
    @NotBlank(message = "number can not be blank")
    private String number; // number on card
    @NotBlank(message = "holderName can not be blank")
    private String holderName; // userName
    @NotBlank(message = "cvc can not be blank")
    private String cvc; // cvv
    @NotBlank(message = "expiryDate can not be blank")
    private String expiryDate; // format should be month-year

}
