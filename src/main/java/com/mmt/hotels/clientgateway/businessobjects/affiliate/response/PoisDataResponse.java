package com.mmt.hotels.clientgateway.businessobjects.affiliate.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliatePoisCategory;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PoisDataResponse extends AffiliateBaseResponseV2 {
    private List<AffiliatePoisCategory> poiData;
    @JsonIgnore
    private Integer totalCount;
    @JsonIgnore
    private Boolean hasMore;
}