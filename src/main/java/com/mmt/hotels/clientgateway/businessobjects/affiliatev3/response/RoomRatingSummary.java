package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class RoomRatingSummary {

    private String roomCode;
    private String category;
    private String title;
    private float rating;
    private int reviewCount;
    private List<SubCategories> subCategories;
    private List<Reviews> reviews;
}
