package com.mmt.hotels.clientgateway.businessobjects.affiliate.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AffiliateHoldBookingResponse extends AffiliateBaseResponse {
    private String txnKey;
    private String paymentRespMessage;
    private String bookingID;
    private String checkoutId;
    private String currency;
    private String totalAmount;
    private String payId;
}