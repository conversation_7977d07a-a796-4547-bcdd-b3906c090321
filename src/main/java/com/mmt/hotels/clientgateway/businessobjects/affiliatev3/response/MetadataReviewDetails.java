package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class MetadataReviewDetails {

    private Float rating;
    private int totalReviewCount;
    private int totalRatingCount;
    private int hotelierReplyCount;
    private List<SubRatingsDetails> subRatings;
    private List<Object> recentRatings;
    private Map<String, Integer> ratingBreakup;
    private Map<String, Integer> reviewBreakup;
    private String countryCode;
    private List<Reviews> reviews;
    private RatingSummary ratingSummary;
    private MetadataMediaDetails media;
    private List<TravelTypes> travelTypes;
    private List<SortingCriteria> sortingCriterions;
    private List<ImageTagDetails> imageTagCount;
    private String ratingLabel;
    private String bestReviewTitle;
    private RatingPersuasion ratingPersuasion;
}
