package com.mmt.hotels.clientgateway.businessobjects.affiliate.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CountriesDataResponseV2 extends AffiliateBaseResponseV2 {

    private List<AffiliateCountryV2> countries;
    @JsonIgnore
    private Integer totalCount;
    @JsonIgnore
    private Boolean hasMore;
}
