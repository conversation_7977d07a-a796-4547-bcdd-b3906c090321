package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class RoomImagesDetails {

    private String url;
    private String roomCode;
    private String imageFilterInfo;
}
