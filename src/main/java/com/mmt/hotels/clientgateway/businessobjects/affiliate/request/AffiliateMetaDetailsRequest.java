package com.mmt.hotels.clientgateway.businessobjects.affiliate.request;

import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateDeviceDetails;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateMetaSearchCriteria;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateVisitorDetails;
import com.mmt.hotels.clientgateway.request.ImageDetails;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

public class AffiliateMetaDetailsRequest extends AffiliateBaseRequest {

    @Valid
    private ImageDetails imageDetails;

    @NotNull
    @Valid
    private AffiliateVisitorDetails visitorDetails;

    @NotNull
    @Valid
    private AffiliateMetaSearchCriteria searchCriteria;

    private AffiliateDeviceDetails deviceDetails;

    private List<String> uuids;

    public List<String> getUuids() {
        return uuids;
    }

    public void setUuids(List<String> uuids) {
        this.uuids = uuids;
    }

    public ImageDetails getImageDetails() {
        return imageDetails;
    }

    public void setImageDetails(ImageDetails imageDetails) {
        this.imageDetails = imageDetails;
    }

    public AffiliateVisitorDetails getVisitorDetails() {
        return visitorDetails;
    }

    public void setVisitorDetails(AffiliateVisitorDetails visitorDetails) {
        this.visitorDetails = visitorDetails;
    }

    public AffiliateMetaSearchCriteria getSearchCriteria() {
        return searchCriteria;
    }

    public void setSearchCriteria(AffiliateMetaSearchCriteria searchCriteria) {
        this.searchCriteria = searchCriteria;
    }

    public AffiliateDeviceDetails getDeviceDetails() {
        return deviceDetails;
    }

    public void setDeviceDetails(AffiliateDeviceDetails deviceDetails) {
        this.deviceDetails = deviceDetails;
    }

}