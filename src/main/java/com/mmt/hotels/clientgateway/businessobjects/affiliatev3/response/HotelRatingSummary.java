package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class HotelRatingSummary {

    private String category;

    @JsonProperty("category")
    public String getCategory() {
        return category;
    }

    @JsonProperty("concept")
    public void setCategory(final String category) {
        this.category = category;
    }

    @JsonProperty("title")
    public String getTitle() {
        return title;
    }


    @JsonProperty("displayText")
    public void setTitle(final String title) {
        this.title = title;
    }

    @JsonProperty("rating")
    public int getRating() {
        return rating;
    }

    @JsonProperty("value")
    public void setRating(final int rating) {
        this.rating = rating;
    }

    @JsonProperty("subCategories")
    public List<SubCategories> getSubCategories() {
        return subCategories;
    }

    @JsonProperty("subConcepts")
    public void setSubCategories(final List<SubCategories> subCategories) {
        this.subCategories = subCategories;
    }

    private String title;
    private int rating;
    private int reviewCount;
    private List<SubCategories> subCategories;
}
