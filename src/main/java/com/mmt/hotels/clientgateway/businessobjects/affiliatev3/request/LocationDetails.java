package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.request;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class LocationDetails {

    @NotBlank
    private String id;
    @NotBlank
    private String type;
    private String countryId;
    private GeoLocationDetails geo;
}
