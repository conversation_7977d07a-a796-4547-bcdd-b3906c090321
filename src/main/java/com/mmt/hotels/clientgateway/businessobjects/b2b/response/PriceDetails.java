package com.mmt.hotels.clientgateway.businessobjects.b2b.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.response.AffiliateV2PriceCouponInfo;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class PriceDetails {
    private int totalRoomCount;
    private Double basePrice;
    private String displayPriceType;
    private Double totalTax;
    private Double couponDiscount;
    private Double hotelDiscount;
    private DiscountDetails discount;
    private List<AffiliateV2PriceCouponInfo> applicableCoupons;
    private TaxBreakupDetails taxBreakUp;
    private HCPDetails hcp;
    private ExtraAdultDetails extraAdult;
    private String segmentId;
    private Double currencyConvertor;
}
