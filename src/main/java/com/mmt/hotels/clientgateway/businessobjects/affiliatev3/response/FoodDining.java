package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class FoodDining {

    private String category;
    private String id;
    private boolean showInHost;
    private String hostCatHeading;
    private boolean showInDetailHome;
    private boolean expandRules;
    private List<DiningRules> rules;
    private List<String> images;
    private String summaryText;
}
