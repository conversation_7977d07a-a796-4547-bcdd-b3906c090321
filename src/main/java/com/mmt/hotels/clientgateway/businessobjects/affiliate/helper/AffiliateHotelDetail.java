package com.mmt.hotels.clientgateway.businessobjects.affiliate.helper;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Set;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AffiliateHotelDetail {
    private String id;
    private Set<String> pois;
    private Set<String> areas;
    private AffiliateHotelLocation location;
}
