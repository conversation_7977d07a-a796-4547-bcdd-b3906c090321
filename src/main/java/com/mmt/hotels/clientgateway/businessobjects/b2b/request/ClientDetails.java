package com.mmt.hotels.clientgateway.businessobjects.b2b.request;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ClientDetails {
    private String visitorId;
    private String deviceId;
    private String uuid;
    @NotBlank
    private String deviceType;
    @NotNull
    private ProfileType profileType;
    private String mcId;
    private String appVersion;
    private String os;
    private String brand;
    @NotBlank
    private String pageContext;
    @NotNull
    private FunnelSource funnelSource;
    @NotBlank
    private String idContext;
}
