package com.mmt.hotels.clientgateway.businessobjects.b2b.request;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FilterLocationDetails {

    private String id;
    private FilterLocationType type;
    private String name;
    private GeoLocationDetails geo;
}
