package com.mmt.hotels.clientgateway.businessobjects.affiliate.helper;

import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AffiliateSearchCriteria {

	@NotEmpty
	private String checkIn;
	@NotEmpty
	private String checkOut;
	@Valid
	private AffiliateGuestDetails guestDetails;

	@Valid
	private AffiliateLocation locationDetails;
	private String lastHotelId;
	private String lastFetchedHotelCategory;

	private String currency;
	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getCheckIn() {
		return checkIn;
	}

	public void setCheckIn(String checkIn) {
		this.checkIn = checkIn;
	}

	public String getCheckOut() {
		return checkOut;
	}

	public void setCheckOut(String checkOut) {
		this.checkOut = checkOut;
	}

	public AffiliateGuestDetails getGuestDetails() {
		return guestDetails;
	}

	public void setGuestDetails(AffiliateGuestDetails guestDetails) {
		this.guestDetails = guestDetails;
	}

	public AffiliateLocation getLocationDetails() {
		return locationDetails;
	}

	public void setLocationDetails(AffiliateLocation locationDetails) {
		this.locationDetails = locationDetails;
	}

	public String getLastHotelId() {
		return lastHotelId;
	}

	public void setLastHotelId(String lastHotelId) {
		this.lastHotelId = lastHotelId;
	}

	public String getLastFetchedHotelCategory() {
		return lastFetchedHotelCategory;
	}

	public void setLastFetchedHotelCategory(String lastFetchedHotelCategory) {
		this.lastFetchedHotelCategory = lastFetchedHotelCategory;
	}
}