package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.*;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.response.RoomDetailsV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode
@Data
public class TariffInfo {

    private String roomTypeName;
    private StayDetails stayDetails;
    private List<InclusionDetails> inclusions;
    private List<AffiliateMealPlan> mealPlans;
    private SupplierDetails supplierDetails;
    private RoomDetailsV2 roomDetails;
    private TariffDisplayFare displayFare;
}
