package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class RecheckRoomEntity {

    private String name;
    private String code;
    private List<RecheckResponseRatePlan> ratePlans;
    private List<String> roomImages;
    private PriceDetails price;
}
