package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateCancelRules;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateMealPlan;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.response.AffiliateV2RatePlanInclusions;
import com.mmt.hotels.model.response.pricing.PaymentMode;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RatePlan {

	private String rateType;
	private Boolean hotelCloud;
	private String code;
	private String description;
	private String rateCheckId;
	private String segmentId;
	private String vendorRoomCode;
	private List<Occupancy> occupancies;
	private String reviewDeeplinkUrl;
	private String instantConfirmation;
	private boolean guarantee;
	private boolean bnpl;
	private boolean staycationDeal;
	private boolean allInclusiveRate;
	private boolean packageRatePlan;
	private boolean checkInclusions;
	private boolean checkCancellationPolicy;
	private List<AffiliateV2RatePlanInclusions> inclusions;
	private List<AffiliateMealPlan> mealPlans;
	private PaymentMode paymentMode;
	private String originalAPPayMode;
	private AvailDetails availDetail;
	private CancellationPolicy cancellationPolicy;
	private CancellationTimelineDetails cancellationTimeline;
	private List<AffiliateCancelRules> cancelRules;
	private PriceDetails price;
}
