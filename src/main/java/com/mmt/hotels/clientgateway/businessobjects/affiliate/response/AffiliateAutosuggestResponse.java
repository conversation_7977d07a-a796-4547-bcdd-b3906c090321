package com.mmt.hotels.clientgateway.businessobjects.affiliate.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.responseV2.AffiliateV2BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AffiliateAutosuggestResponse extends AffiliateV2BaseResponse {
    private List<AffiliateAutosuggestion> autosuggestions;
}
