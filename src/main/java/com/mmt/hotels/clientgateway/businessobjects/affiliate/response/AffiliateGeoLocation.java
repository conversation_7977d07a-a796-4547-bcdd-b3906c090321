package com.mmt.hotels.clientgateway.businessobjects.affiliate.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AffiliateGeoLocation {
    @JsonProperty("lat")
    @JsonAlias("latitude")
    private String latitude;
    @JsonProperty("long")
    @JsonAlias("longitude")
    private String longitude;
}
