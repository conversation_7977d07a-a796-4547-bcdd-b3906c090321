package com.mmt.hotels.clientgateway.businessobjects.b2b.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class CancellationTimelineDetails {

    private String checkInDate;
    private String checkInDateTime;
    private String cancellationDate;
    private String cancellationDateTime;
    private String cancellationDateInDateFormat;
    private String cardChargeDate;
    private String cardChargeDateLong;
    private String cardChargeDateTime;
    private String cardChargeTime;
    private String dateFormat;
    private String cardChargeText;
    private String bookingAmountText;
    private String subTitle;
    private String freeCancellationText;
    private String title;
    private String bookingDate;
    private List<FreeCancellationBenefitDetails> freeCancellationBenefits;
    private String fcTextForPersuasion;
    List<CancellationPolicyTimelineDetails> cancellationPolicyTimelineList;
    private String tillDate;
    private String cardChargeTextTitle;
    private String cardChargeTextMsg;
    private String bnplTitleText;
}
