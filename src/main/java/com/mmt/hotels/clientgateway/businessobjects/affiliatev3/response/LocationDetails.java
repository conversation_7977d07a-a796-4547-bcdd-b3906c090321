package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class LocationDetails {

    private String id;
    private String name;
    private String type;
    private String countryId;
    private String countryName;
    private String state;
    private String baseCityName;
    private GeoLocationDetails geo;
    private String stateId;
    private String cityName;
}
