package com.mmt.hotels.clientgateway.businessobjects.affiliate.helper;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AffiliateNotices {

    private String name;
    private String description;
    private String subCategory;
    private String leafCategory;
    private String currency;
    private Double amount;
    private boolean additionalFee;

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSubCategory() {
        return this.subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public String getLeafCategory() {
        return this.leafCategory;
    }

    public void setLeafCategory(String leafCategory) {
        this.leafCategory = leafCategory;
    }

    public String getCurrency() {
        return this.currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Double getAmount() {
        return this.amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public boolean isAdditionalFee() {
        return this.additionalFee;
    }

    public void setAdditionalFee(boolean additionalFee) {
        this.additionalFee = additionalFee;
    }

}