package com.mmt.hotels.clientgateway.businessobjects.b2b.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.response.AttributesFacility;
import lombok.Data;

import java.util.List;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class Amenities {

    private String category;
    private String displayName;
    private String type;
    private List<String> tags;
    private List<AttributesFacility> attributes;
}
