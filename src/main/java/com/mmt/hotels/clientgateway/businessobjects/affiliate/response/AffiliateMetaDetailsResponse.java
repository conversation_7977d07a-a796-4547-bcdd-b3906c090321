package com.mmt.hotels.clientgateway.businessobjects.affiliate.response;

import com.fasterxml.jackson.databind.JsonNode;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateHomeStayDetails;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateHotelImage;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateHotelResult;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateRoomInfo;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class AffiliateMetaDetailsResponse extends AffiliateBaseResponse {
    private AffiliateHotelResult hotelResult;
    private AffiliateHotelImage image;
    private AffiliateRoomInfo roomInfoData;
    private JsonNode weaverResponse;
    private Map<String, JsonNode> reviewSummary;
    private Set<String> uuids;
    private List<AffiliateHomeStayDetails> homeStayDetails;
}