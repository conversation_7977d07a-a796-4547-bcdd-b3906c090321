package com.mmt.hotels.clientgateway.businessobjects.b2b.request;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MetadataRequest {

    @Valid
    @NotNull
    private ClientDetails client;
    private String hotelId;
    @Valid
    @NotNull
    private LocationDetails location;
    private FeatureDetails features;
}
