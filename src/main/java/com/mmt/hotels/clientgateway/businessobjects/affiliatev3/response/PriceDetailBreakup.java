package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode
@Data
public class PriceDetailBreakup {

    private double nonDiscountedPriceWOTax;
    private double nonDiscountedPriceWithTax;
    private double discountedPriceWOTax;
    private double discountedPriceWithTax;
}
