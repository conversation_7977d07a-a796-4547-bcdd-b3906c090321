package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ValidateCouponV3Request {
    private String blockId;
    private CouponDetails couponDetails;
}
