package com.mmt.hotels.clientgateway.businessobjects.affiliate.helper;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AffiliateSavedLocations {

    String desc;
    String type;
    String latitude;
    String longitude;
    String placeId;

    public String getDesc() {
        return this.desc;
    }

    public String getType() {
        return this.type;
    }

    public String getLatitude() {
        return this.latitude;
    }

    public String getLongitude() {
        return this.longitude;
    }

    public String getPlaceId() {
        return this.placeId;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public void setPlaceId(String placeId) {
        this.placeId = placeId;
    }

}