package com.mmt.hotels.clientgateway.businessobjects.affiliate.helper;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AffiliateWikiDetail {

    private Integer id;
    private String firstPara;
    private String url;
    private String imgUrl;
    private String secondPara;
    private List<String> tags;
    private AffiliateWikiMetaInfo wikiMetaInfo;
    private List<AffiliateWikiTransitTag> transitTags;
    private List<AffiliateWikiInfo> reasonToStays;
    private List<AffiliateWikiPoi> poiTags;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFirstPara() {
        return firstPara;
    }

    public void setFirstPara(String firstPara) {
        this.firstPara = firstPara;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getSecondPara() {
        return secondPara;
    }

    public void setSecondPara(String secondPara) {
        this.secondPara = secondPara;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public AffiliateWikiMetaInfo getWikiMetaInfo() {
        return wikiMetaInfo;
    }

    public void setWikiMetaInfo(AffiliateWikiMetaInfo wikiMetaInfo) {
        this.wikiMetaInfo = wikiMetaInfo;
    }

    public List<AffiliateWikiTransitTag> getTransitTags() {
        return transitTags;
    }

    public void setTransitTags(List<AffiliateWikiTransitTag> transitTags) {
        this.transitTags = transitTags;
    }

    public List<AffiliateWikiInfo> getReasonToStays() {
        return reasonToStays;
    }

    public void setReasonToStays(List<AffiliateWikiInfo> reasonToStays) {
        this.reasonToStays = reasonToStays;
    }

    public List<AffiliateWikiPoi> getPoiTags() {
        return poiTags;
    }

    public void setPoiTags(List<AffiliateWikiPoi> poiTags) {
        this.poiTags = poiTags;
    }

}