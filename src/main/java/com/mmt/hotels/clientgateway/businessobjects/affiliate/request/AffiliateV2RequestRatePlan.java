package com.mmt.hotels.clientgateway.businessobjects.affiliate.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response.Occupancy;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class AffiliateV2RequestRatePlan {
    @NotBlank
    private String ratePlanCode;
    @NotBlank
    private String roomCode;
    @NotBlank
    private String rateCheckId;
    private List<Occupancy> occupancies;
}
