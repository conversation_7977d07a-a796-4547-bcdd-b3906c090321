package com.mmt.hotels.clientgateway.businessobjects.b2b.response;



import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class PersonalizedSectionDetails {

    private String name;
    private String heading;
    private String hotelCardType;
    private String orientation;
    private int hotelCount;
    private boolean cardInsertionAllowed;
    private boolean isMyBizAssuredRecommended;
    private List<HotelDetails> hotels;
}
