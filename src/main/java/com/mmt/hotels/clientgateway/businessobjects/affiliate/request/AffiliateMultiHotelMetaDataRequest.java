package com.mmt.hotels.clientgateway.businessobjects.affiliate.request;

import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateDeviceDetails;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateImageDetails;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateMultiHotelSearchCriteria;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateVisitorDetails;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

public class AffiliateMultiHotelMetaDataRequest extends AffiliateBaseRequest {

    private AffiliateImageDetails imageDetails;

    @NotNull
    @Valid
    private AffiliateVisitorDetails visitorDetails;

    @NotNull
    @Valid
    private AffiliateMultiHotelSearchCriteria searchCriteria;

    @NotNull
    @Valid
    private AffiliateDeviceDetails deviceDetails;


    public AffiliateImageDetails getImageDetails() {
        return imageDetails;
    }

    public void setImageDetails(AffiliateImageDetails imageDetails) {
        this.imageDetails = imageDetails;
    }

    public AffiliateVisitorDetails getVisitorDetails() {
        return visitorDetails;
    }

    public void setVisitorDetails(AffiliateVisitorDetails visitorDetails) {
        this.visitorDetails = visitorDetails;
    }

    public AffiliateMultiHotelSearchCriteria getSearchCriteria() {
        return searchCriteria;
    }

    public void setSearchCriteria(AffiliateMultiHotelSearchCriteria searchCriteria) {
        this.searchCriteria = searchCriteria;
    }

    public AffiliateDeviceDetails getDeviceDetails() {
        return deviceDetails;
    }

    public void setDeviceDetails(AffiliateDeviceDetails deviceDetails) {
        this.deviceDetails = deviceDetails;
    }

}
