package com.mmt.hotels.clientgateway.businessobjects.affiliate.helper;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class AffiliateDisplayPriceBreakDown {
    private double basePrice;
    private double hotelTax;
    private double hotelServiceCharge;
    private double mmtServiceCharge;
    private double mmtDiscount;
    private double totalDiscount;
    private double nonDiscountedPrice;
    private double savingPerc;
    private AffiliatePriceCouponInfo couponInfo;
    private double displayPrice;
    private String pricingKey;
    private boolean isTaxIncluded;
    private double totalTax;
    private double effectivePrice;
    private double totalAmount;
    private double totalSaving;
    private double couponDiscount;
    private String displayPriceType;
}
