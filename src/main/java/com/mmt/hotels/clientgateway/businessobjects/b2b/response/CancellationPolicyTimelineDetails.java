package com.mmt.hotels.clientgateway.businessobjects.b2b.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class CancellationPolicyTimelineDetails {

    private String startDate;
    private String startDateTime;
    private String endDate;
    private String endDateTime;
    private String text;
    private FreeCancellationBenefitDetails fcBenefit;
    private boolean refundable;
    private String refundText;
    private String type;
}
