package com.mmt.hotels.clientgateway.businessobjects.b2b.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class ReviewDetails {

    private int travellerImageCount;
    private String source;
    private Float rating;
    private int totalReviewCount;
    private int totalRatingCount;
    private List<SubRatingsDetails> subRatings;
    private boolean chatGPTSummaryExists;
    private String ratingText;
    private boolean preferredOTA;
    private boolean showUpvote;
    private int mmtReviewCount;
    private boolean recentReview;
}
