package com.mmt.hotels.clientgateway.businessobjects.affiliate.helper;

import org.apache.commons.lang3.StringUtils;

public class AffiliateAlertInfo {

    AlertType mismatchType;
    String errorMessage;
    String expectedValue;
    String actualValue;
    String roomCode;
    String rpcc;

    public AlertType getMismatchType() {
        return mismatchType;
    }

    public void setMismatchType(AlertType mismatchType) {
        this.mismatchType = mismatchType;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getExpectedValue() {
        return expectedValue;
    }

    public void setExpectedValue(String expectedValue) {
        this.expectedValue = expectedValue;
    }

    public String getActualValue() {
        return actualValue;
    }

    public void setActualValue(String actualValue) {
        this.actualValue = actualValue;
    }

    public String getRoomCode() {
        return roomCode;
    }

    public void setRoomCode(String roomCode) {
        this.roomCode = roomCode;
    }

    public String getRpcc() {
        return rpcc;
    }

    public void setRpcc(String rpcc) {
        this.rpcc = rpcc;
    }

    public static enum AlertType {
        PRICE,
        CANCELLATION,
        MEALPLAN,
        RPCC,
        ROOM_CODE;

        private AlertType() {
        }

        public static AlertType fromString(String alertTypeString) {
            if (StringUtils.isBlank(alertTypeString)) {
                return null;
            } else {
                AlertType[] var1 = values();
                int var2 = var1.length;

                for(int var3 = 0; var3 < var2; ++var3) {
                    AlertType alertType = var1[var3];
                    if (alertTypeString.equalsIgnoreCase(alertType.name())) {
                        return alertType;
                    }
                }

                return null;
            }
        }
    }

}