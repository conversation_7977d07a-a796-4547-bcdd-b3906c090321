package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.request;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MetaDataRequest extends BaseRequest{

    private String hotelId;
    @Valid
    @NotNull
    private LocationDetails location;
    private FeatureDetails features;
    private List<String> uuids;
}
