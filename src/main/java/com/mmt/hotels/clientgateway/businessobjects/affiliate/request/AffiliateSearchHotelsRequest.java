package com.mmt.hotels.clientgateway.businessobjects.affiliate.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AffiliateSearchHotelsRequest extends AffiliateBaseRequest {

    @NotNull AffiliateDeviceDetails deviceDetails;
    @NotNull AffiliateVisitorDetails visitorDetails;
    AffiliateImageDetails imageDetails;
    @NotNull AffiliateListingSearchCriteria searchCriteria;
    AffiliateFeatureFlags featureFlags;
    AffiliateSortingCriteria sortCriteria;
    List<AffiliateFilter> filterCriteria;
    String expData;

    public AffiliateDeviceDetails getDeviceDetails() {
        return deviceDetails;
    }

    public void setDeviceDetails(AffiliateDeviceDetails deviceDetails) {
        this.deviceDetails = deviceDetails;
    }

    public AffiliateVisitorDetails getVisitorDetails() {
        return visitorDetails;
    }

    public void setVisitorDetails(AffiliateVisitorDetails visitorDetails) {
        this.visitorDetails = visitorDetails;
    }

    public AffiliateImageDetails getImageDetails() {
        return imageDetails;
    }

    public void setImageDetails(AffiliateImageDetails imageDetails) {
        this.imageDetails = imageDetails;
    }

    public AffiliateListingSearchCriteria getSearchCriteria() {
        return searchCriteria;
    }

    public void setSearchCriteria(AffiliateListingSearchCriteria searchCriteria) {
        this.searchCriteria = searchCriteria;
    }

    public List<AffiliateFilter> getFilterCriteria() {
        return filterCriteria;
    }

    public void setFilterCriteria(List<AffiliateFilter> filterCriteria) {
        this.filterCriteria = filterCriteria;
    }

    public AffiliateFeatureFlags getFeatureFlags() {
        return featureFlags;
    }

    public void setFeatureFlags(AffiliateFeatureFlags featureFlags) {
        this.featureFlags = featureFlags;
    }

    public String getExpData() {
        return expData;
    }

    public void setExpData(String expData) {
        this.expData = expData;
    }

    public AffiliateSortingCriteria getSortCriteria() {
        return sortCriteria;
    }

    public void setSortCriteria(AffiliateSortingCriteria sortCriteria) {
        this.sortCriteria = sortCriteria;
    }
}