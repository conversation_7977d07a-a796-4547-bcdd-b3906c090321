package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode
@Data
public class StayDetails {

    private long checkIn;
    private long checkOut;
    private List<RoomStayCandidates> roomStayCandidates;

}
