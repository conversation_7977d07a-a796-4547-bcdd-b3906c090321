package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class ReviewDetails {

    private Integer travellerImageCount;
    private String source;
    private Float rating;
    private int totalReviewCount;
    private int totalRatingCount;
    private List<SubRatingsDetails> subRatings;
    private Boolean chatGPTSummaryExists;
    private String ratingText;
    private Boolean preferredOTA;
    private Boolean showUpvote;
    private Integer mmtReviewCount;
    private Boolean recentReview;
}
