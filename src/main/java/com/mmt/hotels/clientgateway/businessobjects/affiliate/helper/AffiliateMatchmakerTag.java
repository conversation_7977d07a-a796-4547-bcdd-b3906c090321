package com.mmt.hotels.clientgateway.businessobjects.affiliate.helper;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AffiliateMatchmakerTag {
    private Integer id;
    private String desc;
    private Integer typeId;
    private Integer areaId;
    private String cityName;
    private String poiCategory;
    @JsonIgnore
    private Integer categoryTypeId;
    private String altImgUrl;
    private String label;
    @JsonIgnore
    private Integer preferenceCode;
    private AffiliateWikiDetail wiki;
    private AffiliateLatLongAndBounds matchMakerTagLatLngObject;
    private String locId;
    private String locType;
    private String areaIdStr;
    private List<String> showableEntities;
    private Integer propCount;
    private List<AffiliateWikiQuestion> questions;
    @JsonProperty("isCity")
    private boolean isCity;
    private String type;
    @JsonProperty("isPoi")
    private boolean isPoi;
    private String tagType;
    private String tagId;
    private AffiliateBbLatLong bbox;
    private Integer priority;
    private String distanceText;
    private String cityTagline;
    @JsonProperty("isPivot")
    private Boolean isPivot;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getPoiCategory() {
        return poiCategory;
    }

    public void setPoiCategory(String poiCategory) {
        this.poiCategory = poiCategory;
    }

    public Integer getCategoryTypeId() {
        return categoryTypeId;
    }

    public void setCategoryTypeId(Integer categoryTypeId) {
        this.categoryTypeId = categoryTypeId;
    }

    public String getAltImgUrl() {
        return altImgUrl;
    }

    public void setAltImgUrl(String altImgUrl) {
        this.altImgUrl = altImgUrl;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getPreferenceCode() {
        return preferenceCode;
    }

    public void setPreferenceCode(Integer preferenceCode) {
        this.preferenceCode = preferenceCode;
    }

    public AffiliateWikiDetail getWiki() {
        return wiki;
    }

    public void setWiki(AffiliateWikiDetail wiki) {
        this.wiki = wiki;
    }

    public AffiliateLatLongAndBounds getMatchMakerTagLatLngObject() {
        return matchMakerTagLatLngObject;
    }

    public void setMatchMakerTagLatLngObject(AffiliateLatLongAndBounds matchMakerTagLatLngObject) {
        this.matchMakerTagLatLngObject = matchMakerTagLatLngObject;
    }

    public String getLocId() {
        return locId;
    }

    public void setLocId(String locId) {
        this.locId = locId;
    }

    public String getLocType() {
        return locType;
    }

    public void setLocType(String locType) {
        this.locType = locType;
    }

    public String getAreaIdStr() {
        return areaIdStr;
    }

    public void setAreaIdStr(String areaIdStr) {
        this.areaIdStr = areaIdStr;
    }

    public List<String> getShowableEntities() {
        return showableEntities;
    }

    public void setShowableEntities(List<String> showableEntities) {
        this.showableEntities = showableEntities;
    }

    public Integer getPropCount() {
        return propCount;
    }

    public void setPropCount(Integer propCount) {
        this.propCount = propCount;
    }

    public List<AffiliateWikiQuestion> getQuestions() {
        return questions;
    }

    public void setQuestions(List<AffiliateWikiQuestion> questions) {
        this.questions = questions;
    }

    public boolean isCity() {
        return isCity;
    }

    public void setCity(boolean city) {
        isCity = city;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isPoi() {
        return isPoi;
    }

    public void setPoi(boolean poi) {
        isPoi = poi;
    }

    public String getTagType() {
        return tagType;
    }

    public void setTagType(String tagType) {
        this.tagType = tagType;
    }

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public AffiliateBbLatLong getBbox() {
        return bbox;
    }

    public void setBbox(AffiliateBbLatLong bbox) {
        this.bbox = bbox;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getDistanceText() {
        return distanceText;
    }

    public void setDistanceText(String distanceText) {
        this.distanceText = distanceText;
    }

    public String getCityTagline() {
        return cityTagline;
    }

    public void setCityTagline(String cityTagline) {
        this.cityTagline = cityTagline;
    }

    public Boolean getPivot() {
        return isPivot;
    }

    public void setPivot(Boolean pivot) {
        isPivot = pivot;
    }

}