package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliatePanInfo;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.response.Policies;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RecheckResponse extends BaseResponse{

    private RecheckHotelDetails hotel;
    private List<Policies> policies;
    private List<String> mustReadRules;
    private RecheckSpecialRequest specialRequest;
    private AffiliatePanInfo panInfo;
    private String blockId;
}
