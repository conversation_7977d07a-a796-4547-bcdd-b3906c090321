package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class HotelData {

    private String id;
    private String name;
    private LocationDetails location;
    private String detailDeeplinkUrl;
    private Boolean pahAvailable;
    private Boolean soldOut;
    private Boolean bnplAvailable;
    private Boolean losDealAvailable;
    private Boolean requestToBook;
    private Boolean rtbPreApproved;
    private Boolean rtbAutoCharge;
    private Boolean preApprovalExpired;
    private Boolean allinclusiveAvailable;
    private Boolean lowestRateAllInclusive;
    private Boolean freeCancellationAvailable;
    private Boolean allTarrifPAH;
    private Boolean rtbratePlanPreApproved;
    private Boolean budgetHotel;
    private Boolean altAcco;
    private Boolean breakFastAvailable;
    private double bnplBaseAmount;
    private List<Rooms> rooms;
    private List<RecommendedRoomOption> roomCombos;
    private Boolean staycationDeal;
    private String propertyType;
    private String propertyLabel;
    private Integer starRating;
    private boolean isGstAssured;
    private boolean twoMealAvailable;
    private boolean allMealAvailable;
    private List<String> mustReadRules;

}
