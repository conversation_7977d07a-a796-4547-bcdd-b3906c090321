package com.mmt.hotels.clientgateway.businessobjects.b2b.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class HotelData {

    private String id;
    private String name;
    private LocationDetails location;
    private String detailDeeplinkUrl;
    private boolean pahAvailable;
    private boolean soldOut;
    private boolean bnplAvailable;
    private boolean losDealAvailable;
    private boolean requestToBook;
    private boolean rtbPreApproved;
    private boolean rtbAutoCharge;
    private boolean preApprovalExpired;
    private boolean allinclusiveAvailable;
    private boolean lowestRateAllInclusive;
    private boolean freeCancellationAvailable;
    private boolean allTarrifPAH;
    private boolean rtbratePlanPreApproved;
    private boolean budgetHotel;
    private boolean altAcco;
    private boolean breakFastAvailable;
    private double bnplBaseAmount;
    private List<Rooms> rooms;
    private boolean staycationDeal;

}
