package com.mmt.hotels.clientgateway.businessobjects.affiliate.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateDeviceDetails;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateImageDetails;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateRecheckSearchCriteria;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateVisitorDetails;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AffiliateRecheckRequest extends AffiliateBaseRequest {

    @NotNull @Valid AffiliateRecheckSearchCriteria searchCriteria;
    @NotNull AffiliateVisitorDetails visitorDetails;
    AffiliateImageDetails imageDetails;
    @NotNull AffiliateDeviceDetails deviceDetails;

    public AffiliateRecheckSearchCriteria getSearchCriteria() {
        return searchCriteria;
    }
    
    public void setSearchCriteria(AffiliateRecheckSearchCriteria searchCriteria) {
        this.searchCriteria = searchCriteria;
    }

    public AffiliateVisitorDetails getVisitorDetails() {
        return visitorDetails;
    }

    public void setVisitorDetails(AffiliateVisitorDetails visitorDetails) {
        this.visitorDetails = visitorDetails;
    }

    public AffiliateImageDetails getImageDetails() {
        return imageDetails;
    }

    public void setImageDetails(AffiliateImageDetails imageDetails) {
        this.imageDetails = imageDetails;
    }

    public AffiliateDeviceDetails getDeviceDetails() {
        return deviceDetails;
    }

    public void setDeviceDetails(AffiliateDeviceDetails deviceDetails) {
        this.deviceDetails = deviceDetails;
    }

}