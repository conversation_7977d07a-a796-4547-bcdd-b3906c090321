package com.mmt.hotels.clientgateway.businessobjects.affiliate.helper;

import javax.validation.constraints.NotEmpty;
import java.util.List;

public class AffiliateMultiHotelSearchCriteria{

    @NotEmpty private List<String> hotelIdList;

    private String checkIn;
    private String checkOut;

    private AffiliateGuestDetails guestDetails;

    public String getCheckIn() {
        return checkIn;
    }

    public void setCheckIn(String checkIn) {
        this.checkIn = checkIn;
    }

    public String getCheckOut() {
        return checkOut;
    }

    public void setCheckOut(String checkOut) {
        this.checkOut = checkOut;
    }

    public AffiliateGuestDetails getGuestDetails() {
        return guestDetails;
    }

    public void setGuestDetails(AffiliateGuestDetails guestDetails) {
        this.guestDetails = guestDetails;
    }

    public List<String> getHotelIdList() {
        return hotelIdList;
    }

    public void setHotelIdList(List<String> hotelIdList) {
        this.hotelIdList = hotelIdList;
    }
}
