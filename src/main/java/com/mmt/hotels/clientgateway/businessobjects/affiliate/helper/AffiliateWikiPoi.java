package com.mmt.hotels.clientgateway.businessobjects.affiliate.helper;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AffiliateWikiPoi {

    private String id;
    private String name;
    private String info;
    private List<String> images;
    private Double lat;
    private Double lng;
    private int order;
    private String cat;
    private String priority;
    private String type;
    private String propCount;
    private List<String> showableEntities;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public String getCat() {
        return cat;
    }

    public void setCat(String cat) {
        this.cat = cat;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPropCount() {
        return propCount;
    }

    public void setPropCount(String propCount) {
        this.propCount = propCount;
    }

    public List<String> getShowableEntities() {
        return showableEntities;
    }

    public void setShowableEntities(List<String> showableEntities) {
        this.showableEntities = showableEntities;
    }

}