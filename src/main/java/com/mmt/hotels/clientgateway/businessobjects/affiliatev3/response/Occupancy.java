package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Occupancy {
    Integer adults;
    Integer children;
    List<Integer> childAges;
    String roomNumber;
}
