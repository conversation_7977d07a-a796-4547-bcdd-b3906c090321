package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateCancelRules;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.response.AffiliateV2ResponseRatePlan;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RecheckResponseRatePlan extends AffiliateV2ResponseRatePlan {

    private String vendorRoomCode;
    private String originalAPPayMode;
    private AvailDetails availDetails;

}
