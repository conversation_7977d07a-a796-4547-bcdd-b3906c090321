package com.mmt.hotels.clientgateway.businessobjects;

import com.mmt.hotels.model.response.pricing.Notices;
import com.mmt.hotels.model.response.pricing.RatePolicy;
import com.mmt.hotels.model.response.staticdata.HouseRules;

import java.util.List;


public class RequestInputBO {
	private String countryCode;
	private String propertyType;
	private boolean isPah;
	private boolean isPahWithCC;
	private String cancellationPolicyType;
	private String cancellationDate;
	private String supplierCode;
	private List<String> mustReadRules;
	private HouseRules houseRules;
	private RatePolicy checkinPolicy;
	private RatePolicy confirmationPolicy;
	private List<Notices> notices;

	public static class Builder {

		private RequestInputBO requestInputBO;

		public Builder() {
			requestInputBO = new RequestInputBO();
		}

		public Builder buildCountryCode(String countryCode) {
			this.requestInputBO.countryCode = countryCode;
			return this;
		}
		
		public Builder buildPropertyType(String propertyType) {
			this.requestInputBO.propertyType = propertyType;
			return this;
		}
		
		public Builder buildHouseRules(HouseRules	houseRules) {
			this.requestInputBO.houseRules = houseRules;
			return this;
		}
		
		public Builder buildMustReadRules(List<String>	mustReadRules) {
			this.requestInputBO.mustReadRules = mustReadRules;
			return this;
		}

		public Builder buildNotices(List<Notices> notices) {
			this.requestInputBO.notices = notices;
			return this;
		}

		public Builder buildPah(boolean pah) {
			this.requestInputBO.isPah = pah;
			return this;
		}
		
		public Builder buildPahWithCC(boolean pahWithCC) {
			this.requestInputBO.isPahWithCC = pahWithCC;
			return this;
		}
		
		public Builder buildCancellationPolicyType(String cancelPolicy) {
			this.requestInputBO.cancellationPolicyType = cancelPolicy;
			return this;
		}
		
		public Builder buildCancellationDate(String cancellationDate) {
			this.requestInputBO.cancellationDate = cancellationDate;
			return this;
		}
		
		public Builder buildSupplierCode(String supplierCode) {
			this.requestInputBO.supplierCode = supplierCode;
			return this;
		}
		
		public Builder buildCheckinPolicy(RatePolicy checkinPolicy) {
			this.requestInputBO.checkinPolicy = checkinPolicy;
			return this;
		}
		
		public Builder buildConfirmationPolicy(RatePolicy confirmationPolicy) {
			this.requestInputBO.confirmationPolicy = confirmationPolicy;
			return this;
		}

		public RequestInputBO build() {
			return this.requestInputBO;
		}
	}
	
	public String getCountryCode() {
		return countryCode;
	}

	public List<String> getMustReadRules() {
		return mustReadRules;
	}

	public HouseRules getHouseRules() {
		return houseRules;
	}

	public String getPropertyType() {
		return propertyType;
	}

	public boolean isPah() {
		return isPah;
	}

	public String getCancellationPolicyType() {
		return cancellationPolicyType;
	}

	public String getCancellationDate() {
		return cancellationDate;
	}

	public String getSupplierCode() {
		return supplierCode;
	}

	public RatePolicy getCheckinPolicy() {
		return checkinPolicy;
	}

	public RatePolicy getConfirmationPolicy() {
		return confirmationPolicy;
	}

	public List<Notices> getNotices() {
		return notices;
	}

	public boolean isPahWithCC() {
		return isPahWithCC;
	}
}
