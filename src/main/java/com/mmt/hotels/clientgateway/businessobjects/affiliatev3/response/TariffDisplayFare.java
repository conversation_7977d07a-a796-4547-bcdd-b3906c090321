package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.DisplayPriceBreakDown;
import com.mmt.hotels.model.response.pricing.HotelPrice;
import com.mmt.hotels.model.response.pricing.TotalTariff;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode
@Data
public class TariffDisplayFare {

    private HotelPrice actualPrice;
    private Map<String, BestCoupon> bestCouponByPaymode;
    private Map<String, List<BestCoupon>> otherCouponByPaymode;
    private TotalTariff totalTariff;
    private DisplayPriceBreakDown displayPriceBreakDown;
}
