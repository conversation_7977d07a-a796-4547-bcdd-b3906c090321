package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.request;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.request.AffiliateV2Guests;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class RoomDetails extends AffiliateV2Guests {
}
