package com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.response.AffiliateV2PriceDetail;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.response.AffiliateV2RoomEntity;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RecommendedRoomOptionListing {
    private String id;
    private String name;
    List<AffiliateV2RoomEntity> rooms;
    private AffiliateV2PriceDetail price;
}
