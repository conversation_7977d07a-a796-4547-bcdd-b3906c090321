package com.mmt.hotels.clientgateway.businessobjects.affiliate.helper;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AffiliateRecommendedRoomTypeDetails {

    List<AffiliateRoomType> roomType;
    AffiliatePriceDetail totalPrice;
    AffiliateAvailOccupancyDetails occupancyDetails;
    AffiliateCancellationTimeline cancellationTimeline;
    String comboMealPlan;
    boolean staycationDeal;
    String comboPayMode;

    public List<AffiliateRoomType> getRoomType() {
        return roomType;
    }

    public void setRoomType(List<AffiliateRoomType> roomType) {
        this.roomType = roomType;
    }

    public AffiliatePriceDetail getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(AffiliatePriceDetail totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getComboMealPlan() {
        return comboMealPlan;
    }

    public void setComboMealPlan(String comboMealPlan) {
        this.comboMealPlan = comboMealPlan;
    }

    public AffiliateAvailOccupancyDetails getOccupancyDetails() {
        return occupancyDetails;
    }

    public void setOccupancyDetails(AffiliateAvailOccupancyDetails occupancyDetails) {
        this.occupancyDetails = occupancyDetails;
    }

    public boolean isStaycationDeal() {
        return staycationDeal;
    }

    public void setStaycationDeal(boolean staycationDeal) {
        this.staycationDeal = staycationDeal;
    }

    public String getComboPayMode() {
        return comboPayMode;
    }

    public void setComboPayMode(String comboPayMode) {
        this.comboPayMode = comboPayMode;
    }

    public AffiliateCancellationTimeline getCancellationTimeline() {
        return cancellationTimeline;
    }

    public void setCancellationTimeline(AffiliateCancellationTimeline cancellationTimeline) {
        this.cancellationTimeline = cancellationTimeline;
    }

}