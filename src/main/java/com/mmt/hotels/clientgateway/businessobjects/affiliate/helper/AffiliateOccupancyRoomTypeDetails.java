package com.mmt.hotels.clientgateway.businessobjects.affiliate.helper;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AffiliateOccupancyRoomTypeDetails {

    boolean staycationDeal;
    AffiliatePriceDetail totalPrice;
    String comboMealPlan;

    public boolean isStaycationDeal() {
        return staycationDeal;
    }

    public void setStaycationDeal(boolean staycationDeal) {
        this.staycationDeal = staycationDeal;
    }

    public AffiliatePriceDetail getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(AffiliatePriceDetail totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getComboMealPlan() {
        return comboMealPlan;
    }

    public void setComboMealPlan(String comboMealPlan) {
        this.comboMealPlan = comboMealPlan;
    }

}