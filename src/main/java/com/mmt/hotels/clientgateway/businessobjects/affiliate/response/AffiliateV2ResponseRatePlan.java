package com.mmt.hotels.clientgateway.businessobjects.affiliate.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateCancelRules;
import com.mmt.hotels.clientgateway.businessobjects.affiliate.helper.AffiliateMealPlan;
import com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response.CancellationTimelineDetails;
import com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response.ExtraAdultDetails;
import com.mmt.hotels.clientgateway.businessobjects.affiliatev3.response.HCPDetails;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AffiliateV2ResponseRatePlan {

    private String code;
    private String description;
    private String rateType;
    private Boolean hotelCloud;
    private String rateCheckId;
    private List<AffiliateMealPlan> mealPlans;
    private List<AffiliateV2RatePlanInclusions> inclusions;
    private String paymentMode;
    private Integer availableRoomCount;
    private AffiliateV2CancellationPolicy cancellationPolicy;
    @JsonIgnore
    private List<AffiliateV2RoomTariff> roomTariff;
    private AffiliateV2PriceDetail price;
    private HCPDetails hcp;
    private ExtraAdultDetails extraAdult;
    private CancellationTimelineDetails cancellationTimeline;
    private List<AffiliateCancelRules> cancelRules;
}
