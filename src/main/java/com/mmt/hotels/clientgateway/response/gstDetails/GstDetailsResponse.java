package com.mmt.hotels.clientgateway.response.gstDetails;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GstDetailsResponse<T> {
    public String correlationKey;
    public Boolean success;
    public String message;
    public String error;
    public T data;
}
