package com.mmt.hotels.clientgateway.response.streaks.balance;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class GotribeBenefits {
    @JsonProperty("background_image")

    String backgroundImage;

    @JsonProperty("title_image")

    String titleImage;

    @JsonProperty("benefits")
    ArrayList<Benefit> benefits;
}
