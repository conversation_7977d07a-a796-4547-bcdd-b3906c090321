package com.mmt.hotels.clientgateway.response.streaks.userinfo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class Review{
    @JsonProperty("show_tracker")
    boolean showTracker;
    @JsonProperty("message")
    String message;
    @JsonProperty("title")
    String title;
    @JsonProperty("subtitle")
    String subtitle;
}

