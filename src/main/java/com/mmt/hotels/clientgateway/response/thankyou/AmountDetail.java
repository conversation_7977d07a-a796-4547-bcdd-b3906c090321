package com.mmt.hotels.clientgateway.response.thankyou;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class AmountDetail {
    private String key;
    private String dueAmountPaymentDate;
    private String title;
    private String subtitle;
    private String currency;
    private Double amount;
    private Double alternateAmount;
    private String alternateCurrency;
    private String myTripsDeeplink;
    private String currencySymbol;
}

