package com.mmt.hotels.clientgateway.response.moblanding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.model.response.staticdata.BboxLocationDetails;
import com.mmt.hotels.model.response.staticdata.MediaData;
import com.mmt.hotels.pojo.LocationRecommendation.LocationDataHighlight;
import com.mmt.hotels.pojo.LocationRecommendation.LocationDataTagDetail;
import com.mmt.hotels.pojo.matchmaker.Context;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class LocationDataTag {
    private String matchmakerId;
    private String matchmakerType;
    private String name;
    private String tagline;
    private String category;
    private LocationDataTagDetail tag;
    private List<MediaData> media;
    private String detail_description;
    private List<LocationDataHighlight> highlights;
    private String cityName;
    private Context context;
    private BboxLocationDetails meta;
}