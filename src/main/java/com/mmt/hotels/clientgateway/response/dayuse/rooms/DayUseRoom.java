package com.mmt.hotels.clientgateway.response.dayuse.rooms;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.response.FacilityGroup;
import com.mmt.hotels.clientgateway.response.rooms.RoomHighlight;
import com.mmt.hotels.clientgateway.response.rooms.RoomTariff;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 25-Feb-2022, Friday 7:51 PM
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DayUseRoom {

    private String roomName;
    private String roomCode;
    private List<FacilityGroup> amenities;
    private List<RoomHighlight> roomHighlights;
    private List<String> images;
    private RoomTariff occupancydetails;

}
