package com.mmt.hotels.clientgateway.response.ugc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(
        ignoreUnknown = true
)
public class ResponseData {
    private String ugcId;
    private String segmentId;
    private boolean flatFlow;
    private Integer numberOfPages;
    private String endCityName;
    private String hotelName;
    private String hotelCityName;
    private String locusId;
    private String locusType;
    private String contentId;
    private String questionnaireRule;
    private int maxAmount;
    private int maxPercent;
    private List<QuestionDetails> submittedQuestions;
    private QuestionDetails nextQuestion;
    private List<QuestionDetails> nextQuestions;
    private List<Level> levelconfig;
    private String programId;
    private Configs configs;
    private String authToken;
    private boolean editReview;
    private String state;
    private String bookingid;
    private ErrorInfo error;
    private QuestionConfig questionConfig;
    private int levelNumber;
    private int numberOfLevels;
    private int pageId;
    private int curLevelPageCount;
    private UnsupportedProgramIdData unsupportedProgramIdData;
}
