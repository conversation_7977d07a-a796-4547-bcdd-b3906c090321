package com.mmt.hotels.clientgateway.response.rooms;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PackageSelectRoomRatePlan extends SelectRoomRatePlan {
  private String extendedCheckInDate;
  private String extendedCheckOutDate;
  private PackageInclusionDetails packageInclusionDetails;
}
