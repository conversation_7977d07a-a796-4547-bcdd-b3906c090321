package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.response.flyfish.UGCPlatformReviewSummaryDTO;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class UGCSummary {
    private String cardTitle;
    private List<OTA> OTA;
    private UGCPlatformReviewSummaryDTO data;
}
