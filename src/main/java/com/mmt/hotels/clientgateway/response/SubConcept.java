package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class SubConcept {

	private String sentiment;
	private String subConcept;
	private String displayText;
	private Integer relatedReviewCount;
	private Integer priorityScore;
	private String tagType;
	private String source;

	public String getSentiment() {
		return sentiment;
	}
	public void setSentiment(String sentiment) {
		this.sentiment = sentiment;
	}
	public String getSubConcept() {
		return subConcept;
	}
	public void setSubConcept(String subConcept) {
		this.subConcept = subConcept;
	}
	public Integer getRelatedReviewCount() {
		return relatedReviewCount;
	}
	public void setRelatedReviewCount(Integer relatedReviewCount) {
		this.relatedReviewCount = relatedReviewCount;
	}
	public Integer getPriorityScore() {
		return priorityScore;
	}
	public void setPriorityScore(Integer priorityScore) {
		this.priorityScore = priorityScore;
	}
	public String getTagType() {
		return tagType;
	}
	public void setTagType(String tagType) {
		this.tagType = tagType;
	}
	public String getDisplayText() {
		return displayText;
	}
	public void setDisplayText(String displayText) {
		this.displayText = displayText;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
}
