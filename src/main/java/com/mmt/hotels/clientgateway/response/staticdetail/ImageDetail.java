package com.mmt.hotels.clientgateway.response.staticdetail;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class ImageDetail {

	private String date;
	
	private String imageFilterInfo;
	
	private List<String> seekTags;
	
	private String title;
	
	private String travellerName;
	
	private String url;
	
	private String roomCode;
}
