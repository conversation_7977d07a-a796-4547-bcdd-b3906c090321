package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

// CalendarBO node is the value corresponding to the key "date" in dates map
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CalendarBO {
    // status field denotes whether the key "date" is available or not
    private String status;
    // price denotes the price of the selected rate plan with checkin as key "date" and los of 1
    private Double price;

    private String priceColor;
}
