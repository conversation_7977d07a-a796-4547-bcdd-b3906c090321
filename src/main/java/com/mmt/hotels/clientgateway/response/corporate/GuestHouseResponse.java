package com.mmt.hotels.clientgateway.response.corporate;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.wrapper.Error;
import com.mmt.hotels.model.request.upsell.Action;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO;
import lombok.Data;

import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class GuestHouseResponse {
    private String message;
    private String status;
    private Integer statusCode;
    private String responseCode;
    private Error error;
    private String title;
    private String subtitle;
    private String ctaText;
    private Map<String, Action> ctaMap;
    private List<Hotel> propertyList;
    private String correlationKey;
}
