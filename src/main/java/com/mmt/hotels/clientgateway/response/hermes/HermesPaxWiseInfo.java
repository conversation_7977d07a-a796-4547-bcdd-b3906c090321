package com.mmt.hotels.clientgateway.response.hermes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HermesPaxWiseInfo {

    @JsonProperty("hmd")
    String hermesMetaData;

    @JsonProperty("fwdp")
    Map<String, String> forwardParams;

    @JsonProperty("cot")
    String contractType;

    @JsonProperty("gseg")
    String segmentId;

    @JsonProperty("pd")
    HermesPriceDetail priceDetail;
}
