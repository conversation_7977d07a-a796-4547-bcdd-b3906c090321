package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.mmt.hotels.clientgateway.request.AddOnState;
import com.mmt.hotels.clientgateway.request.payment.AddOnNode;
import com.mmt.hotels.clientgateway.request.payment.SpecialRequest;
import com.mmt.hotels.clientgateway.response.availrooms.Alert;
import com.mmt.hotels.clientgateway.response.availrooms.CorpData;
import com.mmt.hotels.clientgateway.response.availrooms.DoubleBlackInfo;
import com.mmt.hotels.clientgateway.response.availrooms.EMIAbridgeResponse;
import com.mmt.hotels.clientgateway.response.availrooms.FeatureFlags;
import com.mmt.hotels.clientgateway.response.availrooms.GstInfo;
import com.mmt.hotels.clientgateway.response.availrooms.MyBizQuickPayConfigBO;
import com.mmt.hotels.clientgateway.response.availrooms.PanInfo;
import com.mmt.hotels.clientgateway.response.availrooms.PayLaterCard;
import com.mmt.hotels.clientgateway.response.availrooms.PropertyRules;
import com.mmt.hotels.clientgateway.response.availrooms.TCClauseDetails;
import com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions;
import com.mmt.hotels.clientgateway.response.availrooms.PriceChangeInfo;
import com.mmt.hotels.clientgateway.response.corporate.ApprovingManager;
import com.mmt.hotels.clientgateway.response.corporate.CorpApprovalInfo;
import com.mmt.hotels.clientgateway.response.corporate.CorpTravellerInfo;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.response.rooms.AddOnInfo;
import com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline;
import com.mmt.hotels.clientgateway.response.rooms.PaymentPlan;
import com.mmt.hotels.clientgateway.response.rooms.RatePlan;
import com.mmt.hotels.clientgateway.response.rooms.TripDetailsCard;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.streaks.userinfo.StreaksUserInfoResponse;
import com.mmt.hotels.clientgateway.response.thankyou.SelectedSpecialRequests;
import com.mmt.hotels.clientgateway.response.wrapper.AppInstallStrip;
import com.mmt.hotels.model.clm.ClmPersuasion;
import com.mmt.hotels.model.gocash.GoCashDetails;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.hotels.model.response.addon.SMEData.SubscriptionCardData;
import com.mmt.hotels.model.response.corporate.ItineraryPaxDetails;
import com.mmt.hotels.model.response.corporate.MSMECorpCard;
import com.mmt.hotels.model.response.persuasion.HotelCloudData;
import com.mmt.hotels.model.response.persuasion.MyPartnerLoyaltyResponse;
import com.mmt.hotels.model.response.pricing.PlanInfo;
import com.mmt.hotels.model.response.pricing.ScarcityInfo;
import com.mmt.hotels.model.response.pricing.TcsWidgetInfo;
import com.mmt.hotels.model.response.staticdata.ChatbotInfo;
import com.mmt.hotels.model.response.txn.LoyaltyMessageResponse;
import com.mmt.hotels.model.response.staticdata.FlexibleCheckinInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class AvailRoomsResponse {
    private HotelPermissions hotelPermissions;
    private DormType dormType;
    private ChatbotInfo chatbotInfo;
    private PayLaterCard payLaterCard;
    private MarkUpConfig myPartnerMarkupConfig;
    private TripDetailsCard tripDetailsCard;
	private HotelResult hotelInfo;
    private ScarcityInfo scarcityInfo;
    private List<AddOnNode> addons;
    private AddOnInfo addOnInfo;
    private TotalPricing totalpricing;
    private List<RatePlan> rateplanlist;
    private SpecialRequest specialrequests;
    private PropertyRules propertyRules;
    private PlanInfo intlRoamingInfo;
    private PanInfo panInfo;
    private EMIAbridgeResponse emiDetails;
    private DoubleBlackInfo doubleBlackInfo;
    private MSMECorpCard msmeCorpCard;
    private FeatureFlags featureFlags;
    private BNPLDetails bnplDetails;
    private FullPayment fullPayment;
    private GstInfo gstInfo;
    private TcsInfo tcsInfo;
    private SoldOutCallOut soldOutCallOut;
    private String txnKey;
    private AdditionalMandatoryCharges additionalFees;
    private List<Alert> alerts;
    private Alert campaignAlert;
    private BlackInfo blackInfo;
    private List<TravelerDetail> travellers;
    private SelectedSpecialRequests selectedSpecialRequests;
    private List<ApprovingManager> approvingManagers;
    private CorpApprovalInfo corpApprovalInfo;
    @ApiModelProperty(notes = "Old Cancellation Policy Timeline. This is kept for backward compatibility")
    private CancellationTimeline cancellationTimeline;
    @ApiModelProperty(notes = "New Cancellation Policy Timeline")
    private CancellationPolicyTimeline cancellationPolicyTimeline;
    private PaymentPlan paymentPlan;
    private List<RoomStayCandidate> roomStayCandidates;
    private List<PersuasionResponse> safetyPersuasionList;
    private LinkedHashMap<String, PersuasionResponse> safetyPersuasionMap;
    private List<UpsellOptions> upsellOptions;
    private List<UpsellOptions> downsellOptions;
    private List<String> hydraSegments;
    private CorpData corpData;
    private TCClauseDetails tcClauseDetails;
    private List<CardData> cardData;
    private MyBizQuickPayConfigBO myBizQuickPayConfig;
    private Integer userLoyaltyStatus;
    //In case of Mybiz Decentralized/DarwinBox flow need to send Traveller details in response
    private List<ItineraryPaxDetails> paxDetails;
    /*
    bookNowDetails will contain the required information related to
    Mypartner Fare Hold flow that we need at all pages after the review page call
     */
    private BookNowDetails bookNowDetails;
    /*
    This map will contain persuasions required at Hotel level on review page
     */
    private Map<String,PersuasionResponse> hotelPersuasions;

    // This card contains information required to show persuasion in review page in case of negotiated rate hotel.
    private InstantFareInfo instantFareInfo;
    private JsonNode reviewSummaryGI; //Added to show flifish reviews on review page for GI
    private UGCSummary ugcSummary; //Added to show flifish reviews on review page
    private StreaksUserInfoResponse userStreaksInfoResponse; // To Show streaks Api Data on review page for GI
    private ClmPersuasion clmPersuasion; // To Show CLM persuasion for loggedIn Users for GI
    private String stayType; //Added to show stay type for GI
    private GoCashDetails goCashDetails; // GoCash card details for GI
    private LoyaltyMessageResponse loyaltyMessage; // LoyaltyMessage card for GI
    private RtbCard rtbCard; //New RTB Card on Review Page
    private LoyaltyMessageResponse footerStrip;
    private SubscriptionCardData smeSubscriptionData;
    private String expVariantKeys; /* Pokus variant keys for omniture tracking. */
    private String luckyUserContext;
    private boolean isQuickCheckoutApplicable;
    private boolean reviewPriceHitRequired; // This flag represents the clients whether they should require to hit review-price CG endpoint
    private LuckyUserDetails luckyUserDetails;
    private HighDemand highDemand; //This represents the clients whether they should require to hit review-price CG endpoint
    private MyPartnerLoyaltyResponse myPartnerHero;
    private String ackId;
    private FlexibleCheckinInfo flexibleCheckinInfo;
    private BottomSheet requestCallbackData;
    private RateplansUpgrade rateplansUpgrade;
    private PriceChangeInfo priceChangeInfo;
    private HotelCloudData hotelCloudData;
    private TcsWidgetInfo tcsWidgetInfo;
    private Object reviewCommonsCardData;
    private AppInstallStrip appInstallStrip;
    private Map<String,String> expData;
    private String variantKey;
    private FlexiDetailBottomSheet flexiDetailBottomSheet;
    private AddOnState selectedAddOnState;
    private String trackingText;
    private Map<String, CardInfo> cardsMap;
    private String currency;
    private LuckyData luckyDataV2;
    private boolean downsellOptionsAvailable;
    private Map<String, String> trackingMap;
    private CorpTravellerInfo corpTravellerDetails;
    private boolean showExternalChainMembership; // if loyalty number card can be shown on client for myBiz users
}