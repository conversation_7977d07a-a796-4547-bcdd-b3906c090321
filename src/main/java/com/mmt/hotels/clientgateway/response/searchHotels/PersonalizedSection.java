package com.mmt.hotels.clientgateway.response.searchHotels;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class PersonalizedSection {
	
	private String name;
	private String heading;
	private Boolean headingVisible;
	private String subHeading;
	private List<SectionFeature> sectionFeatures;
	// new nodes for listingUI Exp
	private Integer minHotelsToShow;
	private String seeMoreCTA;
	private String hotelCardType;
	private String sectionBG;
	private ToolTip toolTip;
	private String orientation;
	private Integer hotelCount;
	private List<Hotel> hotels;
	private boolean showMore;
	private Integer minItemsToShow;
	private boolean cardInsertionAllowed;
	private SectionFeature filterInfo;
	private BottomSheet bottomSheet;
	private Boolean myBizSimilarHotel;
	private MyBizStaticCard staticCard;
	private boolean premium;
	private String topHeaderText;
	private String footerDetails;
	private Boolean showIndex; // for showing index with hotel name for specific section. eg - "MOST_BOOKED_HOTELS" section
}
