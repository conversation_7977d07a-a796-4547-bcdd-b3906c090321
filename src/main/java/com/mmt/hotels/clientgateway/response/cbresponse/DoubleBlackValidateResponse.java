package com.mmt.hotels.clientgateway.response.cbresponse;

public class DoubleBlackValidateResponse {
    private String benefitId;
    private boolean bookingEligible;
    private boolean userEligible;
    private String messageHeader;
    private String messageText;
    private boolean moreVerificationRequired;
    private String registeredFirstName;
    private String registeredLastName;
    public String getBenefitId() {
        return benefitId;
    }
    public void setBenefitId(String benefitId) {
        this.benefitId = benefitId;
    }
    public boolean isBookingEligible() {
        return bookingEligible;
    }
    public void setBookingEligible(boolean bookingEligible) {
        this.bookingEligible = bookingEligible;
    }
    public boolean isUserEligible() {
        return userEligible;
    }
    public void setUserEligible(boolean userEligible) {
        this.userEligible = userEligible;
    }
    public String getMessageHeader() {
        return messageHeader;
    }
    public void setMessageHeader(String messageHeader) {
        this.messageHeader = messageHeader;
    }
    public String getMessageText() {
        return messageText;
    }
    public void setMessageText(String messageText) {
        this.messageText = messageText;
    }
    public boolean isMoreVerificationRequired() {
        return moreVerificationRequired;
    }
    public void setMoreVerificationRequired(boolean moreVerificationRequired) {
        this.moreVerificationRequired = moreVerificationRequired;
    }
    public String getRegisteredFirstName() {
        return registeredFirstName;
    }
    public void setRegisteredFirstName(String registeredFirstName) {
        this.registeredFirstName = registeredFirstName;
    }
    public String getRegisteredLastName() {
        return registeredLastName;
    }
    public void setRegisteredLastName(String registeredLastName) {
        this.registeredLastName = registeredLastName;
    }
    @Override
    public String toString() {
        return "DoubleBlackValidateResponse [benefitId=" + benefitId + ", bookingEligible=" + bookingEligible
                + ", userEligible=" + userEligible + ", messageHeader=" + messageHeader + ", messageText=" + messageText
                + ", moreVerificationRequired=" + moreVerificationRequired + ", registeredFirstName="
                + registeredFirstName + ", registeredLastName=" + registeredLastName + "]";
    }

}
