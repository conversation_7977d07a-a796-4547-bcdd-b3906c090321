package com.mmt.hotels.clientgateway.response.hermes.rateplaninfo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommonRatePlanInfo {
    @JsonProperty("rtc")
    String roomTypeCode;

    @JsonProperty("rpc")
    String ratePlanCode;

    @JsonProperty("pm")
    int paymentMode;

    @JsonProperty("cot")
    String contractType;

    @JsonProperty("gseg")
    String segmentId;

    @JsonProperty("offercode")
    String offerCode;

    @JsonProperty("pc")
    String promoCode;
}
