package com.mmt.hotels.clientgateway.response.moblanding;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class Item {
    private String text;
    private String type;
    private String textBoxTitle;
    private String category;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTextBoxTitle() {
        return textBoxTitle;
    }

    public void setTextBoxTitle(String textBoxTitle) {
        this.textBoxTitle = textBoxTitle;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
}
