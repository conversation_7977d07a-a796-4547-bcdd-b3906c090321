package com.mmt.hotels.clientgateway.response;

import com.mmt.hotels.model.response.pricing.AlertInfo;
import lombok.Data;

import java.util.List;

@Data
public class SoldOutCallOut {
    private String iconUrl;
    private String title;
    private String[] subTitles;
    private List<AlertInfo.AlertType> reasons;
    private String bgColor;
    private String titleColor;
    private String priceChange;
}
