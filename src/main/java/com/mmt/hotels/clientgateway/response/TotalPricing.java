package com.mmt.hotels.clientgateway.response;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;

import com.mmt.hotels.clientgateway.response.availrooms.AffiliateFeeDetail;
import com.mmt.hotels.clientgateway.response.corporate.CorpApprovalInfo;
import com.mmt.hotels.clientgateway.response.rooms.PaymentPlan;
import com.mmt.hotels.model.persuasion.response.Persuasion;
import com.mmt.hotels.model.response.pricing.HeroTierUpgradeDetails;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations;
import com.mmt.hotels.model.response.pricing.MyPartnerCashbackDetails;
import com.mmt.model.ExpressCheckoutDetail;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class TotalPricing {

	private Map<String, LinkedRatePriceCalculations> linkedRatePriceCalculationsMap;
	private String currency;
	private List<PricingDetails> details;
	private List<PricingDetails> partnerDetails;
	private String offersAppliedText;
	private List<BookedInclusion> offersInclusionsList;
	private List<Coupon> coupons;
	private BenefitDeals benefitDeals;
	private int maxCouponsToShow;
	private String noCouponText;
	private EMIDetail emiBankDetails;
	private EMIPlanDetail emiPlanDetail;
	private List<AffiliateFeeDetail> affiliateFeeDetails;
	private CorpApprovalInfo corpApprovalInfo;
	private String couponSubtext; // to send gift card text HTL-38313
	private String pricingKey;
	private String priceDisplayMsg;
	private String priceSuffix;
	private String priceToolTip;
	private String priceTaxMsg;
	private String couponDesc;
	private double couponAmount;
	private String payAtHotelText;
	private String groupPriceText;
	private String savingsText;
	private Map<String, Persuasion> pricePersuasions;
	private boolean pinCodeMandatory; //[HTL-46133] Added a boolean to show gst card at review page or not for all clients.

	// This field will be set when bnpl is removed due to addition of insurance addon or
	// when a coupon is applied for which coupon is not applicable
	private String bnplUnavailableMsg;

	//HTL-39856 node sent to client if user is eligible for wallet payment on myPartner (Express-Checkout)
	private ExpressCheckoutDetail expressCheckoutDetail;

	//HTL-39744 "payment to be done in rs." string to be shown in indian funnel and international hotel when currency is not INR
	private String paymentInfoText;
	private PriceFooter priceFooter;
	private CouponPersuasion couponPersuasion;
	private String originalPriceMsg; // display (price + taxes) text for MMT black and Gi Gotribe
	private double originalPrice; // display (price + taxes) for Gi Gotribe
	private String linkedRPDiscountMsg;
	private String linkedRPBottomSheetTitle;
	private String linkedRPOriginalPriceMsg;
	private MyPartnerCashbackDetails myPartnerCashbackDetails; //to show myPartner cashback and breakdown details
	private HeroTierUpgradeDetails heroTierUpgradeDetails; //to show updated tier upgrade details

}
