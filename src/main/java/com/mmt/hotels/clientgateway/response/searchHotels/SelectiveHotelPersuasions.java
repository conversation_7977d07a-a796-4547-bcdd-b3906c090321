package com.mmt.hotels.clientgateway.response.searchHotels;

import com.mmt.hotels.model.response.searchwrapper.CollectionCardPersuasion;

import java.util.List;

import lombok.Data;

@Data
public class SelectiveHotelPersuasions {

    CollectionCardPersuasion location;
    CollectionCardPersuasion discount;
    CollectionCardPersuasion promoCash;
    String dealTag;
    Integer totalRoomCount;
    boolean isAltAcco;
    String imageUrl;
    List<CollectionCardPersuasion> inclusions;
    String hotelTag;

}
