package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CalendarAvailabilityResponse {
    // dates field is a map which shows the availability of dates present in its key
    private Map<String, CalendarBO> dates;

    private String currency;

    public Map<String, CalendarBO> getDates() {
        return dates;
    }

    public void setDates(Map<String, CalendarBO> dates) {
        this.dates = dates;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public static class Builder {
        private CalendarAvailabilityResponse calendarAvailabilityResponse;

        public Builder() {
            calendarAvailabilityResponse = new CalendarAvailabilityResponse();
        }

        public CalendarAvailabilityResponse.Builder buildDates(Map<String, CalendarBO> dates) {
            calendarAvailabilityResponse.dates = dates;
            return this;
        }

        public CalendarAvailabilityResponse build() {
            return calendarAvailabilityResponse;
        }

    }
}
