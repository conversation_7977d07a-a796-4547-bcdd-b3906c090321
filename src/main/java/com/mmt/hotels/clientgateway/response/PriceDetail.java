package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class PriceDetail {
	
	private Double displayPriceAltCurrency;
	private Double basePrice;
	private Double displayPrice;
	private Double effectivePrice;
	private Double mmtDiscount;
	private Double cdfDiscount;
	private Double discount;
	private Double wallet;
	private Double nonDiscountedPrice;
	private Double savingPerc;
	private Double price;
	private Double priceWithTax;
	private Double discountedPrice;
	private Double discountedPriceWithTax;
	private Double totalSaving;
	private Double totalTax;
	private Double hotelTax;
	private EMIDetail emiDetails;
	private Coupon coupon;
	private String pricingKey;
	private Double myPartnerDiscount;
	private Map<String, String> metaInfo;
	private String groupPriceText;
	private String savingsText;
	private String hotelSearchDeeplink;
	private Double totalDiscount;
	//A new groupPrice node added for new design on listing page which will be set if GRPN Exp data is T
	private GroupPrice groupPrice;
	private String priceSuffix;
	private String taxesAndFeesText;
}
