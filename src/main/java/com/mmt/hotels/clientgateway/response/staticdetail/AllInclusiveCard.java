package com.mmt.hotels.clientgateway.response.staticdetail;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class AllInclusiveCard {
    private Double amount;
    private List<AllInclusiveCardData> data;
    private String desc;
    private String ratePlanCode;
    private String roomCode;
    private String title;
    private String imageUrl;
    private String persuasionText;
}
