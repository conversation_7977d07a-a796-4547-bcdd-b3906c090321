package com.mmt.hotels.clientgateway.response.gi.taggedmedia;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
@JsonInclude(value = JsonInclude.Include.NON_EMPTY, content = JsonInclude.Include.NON_NULL)
@Data
public class HotelMedia {
    @JsonProperty("taggedMedia_v3")
    private List<TaggedMedia> taggedMediaV3;
}
