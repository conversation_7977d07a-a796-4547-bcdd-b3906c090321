package com.mmt.hotels.clientgateway.response.rooms;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class CancellationTimelineV2 {
    private String startDateText;
    private String startDateSubText;
    private String endDateText;
    private String endDateSubText;
    private String refundText;
    private String type;
    private FlexiCancellationDetails flexiCancellationDetails;
}
