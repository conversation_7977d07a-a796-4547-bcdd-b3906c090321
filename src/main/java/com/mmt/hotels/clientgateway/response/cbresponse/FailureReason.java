package com.mmt.hotels.clientgateway.response.cbresponse;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FailureReason {
    private String msg;
    private String errorCode;

    public FailureReason() {
        /*
         * empty
         */
    }

    public FailureReason(String msg, String errorCode) {
        this.msg = msg;
        this.errorCode = errorCode;
    }

    public String getMsg() {
        return msg;
    }

    public FailureReason setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
}