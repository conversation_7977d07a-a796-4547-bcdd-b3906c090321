package com.mmt.hotels.clientgateway.response.staticdetail;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mmt.hotels.model.response.staticdata.LatLong;
import com.mmt.hotels.model.response.staticdata.StreetInfoCard;
import com.mmt.hotels.model.response.staticdata.LocationCard;
import lombok.Data;

@Data
public class StreetViewInfo {

    @JsonProperty("location")
    private LatLong latLong;
    private String panoId;
    private StreetInfoCard streetInfoCard;
    private LocationCard locationCard;
    private String title;
    private String subTitle;
    private String iconurl;
}
