package com.mmt.hotels.clientgateway.response.searchHotels;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class HotelCard {
    private String heading;
    private String subHeading;
    private String roomSubHeading;
    private Boolean showCard;
    private String cta;
    private String tag;
    private List<Facility> amenities;
    private DesktopStylingClassesObj desktopStylingClassesObj;
    private BGLinearGradient bgLinearGradient;
}
