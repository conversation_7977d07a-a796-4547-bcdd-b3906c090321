package com.mmt.hotels.clientgateway.response.ugc;

import lombok.Data;

@Data
public class Configs {
    private Guidelines guidelines;
    private OnBoardingData onBoardingData;
    private String estimatedTime;
    private String exitReviewString;

    public Configs() {
        // Initialize nested objects
        this.guidelines = new Guidelines();
        this.onBoardingData = new OnBoardingData();

    }
}
