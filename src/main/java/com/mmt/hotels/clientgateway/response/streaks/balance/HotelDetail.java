package com.mmt.hotels.clientgateway.response.streaks.balance;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class HotelDetail {
    @JsonProperty("title")

    String title;

    @JsonProperty("html_subtitle")

    String htmlSubtitle;

    @JsonProperty("booking_state")

    String bookingState;
}
