package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RtbCard {

    private String title;
    @Deprecated
    private String subTitle;
    private String bnplText;
    private String checkBoxText;
    private String checkBoxErrorText;
    private List<String> rtbInfoList;
    private String type;
    private String persuasionIcon;
    private String persuasionText;
}
