package com.mmt.hotels.clientgateway.response.hermes.rateplaninfo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mmt.hotels.clientgateway.response.hermes.HermesPaxWiseInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MultiPaxRatePlanInfo extends CommonRatePlanInfo{
    @JsonProperty("pwi")
    List<HermesPaxWiseInfo> paxWiseInfoList;
}
