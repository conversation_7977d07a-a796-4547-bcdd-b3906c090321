package com.mmt.hotels.clientgateway.response.cbresponse;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mmt.hotels.clientgateway.response.moblanding.GenericErrorEntity;

import java.util.ArrayList;
import java.util.List;


public class ErrorResponse {
    @JsonProperty("errorList")
    private List<GenericErrorEntity> errorList = new ArrayList<GenericErrorEntity>();

    public List<GenericErrorEntity> getErrorList() {
        return errorList;
    }

    public ErrorResponse(){
        //Empty
    }

    public ErrorResponse(List<GenericErrorEntity> errorList){
        this.errorList = errorList;
    }

    public void setErrorList(List<GenericErrorEntity> errorList) {
        this.errorList = errorList;
    }

    @Override
    public String toString() {
        return "ErrorResponse [errorList=" + errorList + "]";
    }




}