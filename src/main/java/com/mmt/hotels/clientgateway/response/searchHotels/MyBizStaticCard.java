package com.mmt.hotels.clientgateway.response.searchHotels;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class MyBizStaticCard implements Serializable {
    private String text;
    private String subtext;
    private String iconUrl;
    private String ctaText;
    private String actionUrl;
}
