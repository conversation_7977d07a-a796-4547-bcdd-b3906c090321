package com.mmt.hotels.clientgateway.response.moblanding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.model.response.staticdata.SimplifiedBoundary;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class BBoxLocationDetails {

    private Polygon polygon;

    private LatLong latLong;

    private Centre centre;

    private Long radius;
    private SimplifiedBoundary simplifiedBoundary;
    private Integer zoomLevel;
    private LatLong bbox;

}
