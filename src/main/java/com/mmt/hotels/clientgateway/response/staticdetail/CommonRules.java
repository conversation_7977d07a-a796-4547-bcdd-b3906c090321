package com.mmt.hotels.clientgateway.response.staticdetail;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommonRules implements Serializable {

    private static final long serialVersionUID = -8206238303857294561L;
    private String category;
    private String id;
    private boolean showInHost;
    private String hostCatHeading;
    private List<Rule> rules;
    private List<String> rulesList;
    private boolean showInDetailHome;
    private boolean expandRules;
    private String uspText;

    //Integer House rule node ids from hotstore
    private Integer categoryId;

    private String description; // This node is added to so that the clients can Identify position to show the text at L2 overview HTL-39055

    List<String> images;
    private List<CommonRules> subCategories;

    private String categoryDesc;
    private String categoryName;
    private String categoryHeading;
    private String ruleDesc;
    private RuleTableInfo ruleTableInfo;
    private boolean showInL2Page = true;
    private String mergeId;
    private boolean showArrowInDetailHome = true;

    public String getMergeId() {
        return mergeId;
    }

    public void setMergeId(String mergeId) {
        this.mergeId = mergeId;
    }

    public String getCategoryDesc() {
        return categoryDesc;
    }

    public void setCategoryDesc(String categoryDesc) {
        this.categoryDesc = categoryDesc;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public String getUspText() {
        return uspText;
    }

    public void setUspText(String uspText) {
        this.uspText = uspText;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCategoryHeading() {
        return categoryHeading;
    }

    public void setCategoryHeading(String categoryHeading) {
        this.categoryHeading = categoryHeading;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public boolean isShowArrowInDetailHome() {
        return showArrowInDetailHome;
    }

    public void setShowArrowInDetailHome(boolean showArrowInDetailHome) {
        this.showArrowInDetailHome = showArrowInDetailHome;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public RuleTableInfo getRuleTableInfo() {
        return ruleTableInfo;
    }

    public void setRuleTableInfo(RuleTableInfo ruleTableInfo) {
        this.ruleTableInfo = ruleTableInfo;
    }

    public List<Rule> getRules() {
        return rules;
    }

    public void setRules(List<Rule> rules) {
        this.rules = rules;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
        setId(category);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        if(StringUtils.isNotBlank(id)) {
            id = id.replaceAll("([^A-Za-z0-9])", "").replaceAll("\\s", "");
            this.id = id;
        }
    }

    public boolean isShowInHost() {
        return showInHost;
    }

    public void setShowInHost(boolean showInHost) {
        this.showInHost = showInHost;
    }

    public String getHostCatHeading() {
        return hostCatHeading;
    }

    public void setHostCatHeading(String hostCatHeading) {
        this.hostCatHeading = hostCatHeading;
    }

    @Override
    public String toString() {
        return "CommonRules{" +
                "category='" + category + '\'' +
                ", rules=" + rules +
                ", id=" + id +
                '}';
    }

    public List<String> getRulesList() {
        return rulesList;
    }

    public void setRulesList(List<String> rulesList) {
        this.rulesList = rulesList;
    }

    public boolean isShowInDetailHome() {
        return showInDetailHome;
    }

    public void setShowInDetailHome(boolean showInDetailHome) {
        this.showInDetailHome = showInDetailHome;
    }

    public boolean isExpandRules() {
        return expandRules;
    }

    public void setExpandRules(boolean expandRules) {
        this.expandRules = expandRules;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public List<CommonRules> getSubCategories() {
        return subCategories;
    }

    public void setSubCategories(List<CommonRules> subCategories) {
        this.subCategories = subCategories;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getShowInL2Page() {
        return showInL2Page;
    }

    public void setShowInL2Page(boolean showInL2Page) {
        this.showInL2Page = showInL2Page;
    }
}
