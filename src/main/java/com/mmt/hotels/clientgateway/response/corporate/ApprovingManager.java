package com.mmt.hotels.clientgateway.response.corporate;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class ApprovingManager {

    private Integer id;
    private String name;
    private String businessEmailId;
    private Integer managerType;
    private String businessEmailCommId;
    private String mmtUuid;
    private String phoneNumber;
    private String phoneNumberCommId;
    private String designation;
    private String employeeId;

}
