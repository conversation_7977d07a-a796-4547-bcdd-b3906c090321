package com.mmt.hotels.clientgateway.response.moblanding;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 08-Dec-2021, Wednesday 12:24 PM
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AdTechSearchContextDetails {

    private String lob;
    private AdTechDestination destination;
    private long startDate;
    private long endDate;
    private String tripType;
    private AdTechHotel hotels;
    private String correlationKey;
    private String searchSource;

}