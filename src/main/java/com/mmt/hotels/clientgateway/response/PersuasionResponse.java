package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;

import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PersuasionResponse {
	
	private String id;
	private String persuasionType;
	private String placeholderId;
	private String persuasionText;
	private String image_url;
	private String backgroundImage;
	private boolean html;
	private String template;
	private Style style;
	private String bgBorderColor;
	private String displaySubText;
	private String displayText;
	private String subText;
	private String title;
	private String iconUrl;
	private Hover hover;
	private String iconType;
	private SubInfo subInfo;
	private String titleColor;
	private List<PersuasionData> persuasions;
	private ValueStaysTooltip tooltip;
}

