package com.mmt.hotels.clientgateway.response.streaks.userinfo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class PageDetails{
    @JsonProperty("hotel_landing")
    HotelLanding hotelLanding;
    @JsonProperty("srp_unfiltered")
    SrpUnfiltered srpUnfiltered;
    @JsonProperty("gostay_filtered_srp")
    GostayFilteredSrp gostayFilteredSrp;
    @JsonProperty("hotel_details")
    HotelDetails hotelDetails;
    @JsonProperty("review")
    Review review;
    @JsonProperty("thank_you")
    ThankYou thankYou;
    @JsonProperty("post_sales")
    PostSales postSales;
}

