package com.mmt.hotels.clientgateway.response.rooms;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import com.mmt.hotels.model.response.pricing.ChildAgesBucket;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RoomTariff {

	private int numberOfAdults;
	private int numberOfChildren;
	private List<Integer> childAges;
	private Integer roomCount;
	private double displayPrice;
	private int roomId; // node will be consumed at affiliate gateway
	private List<ChildAgesBucket> childBuckets;
}
