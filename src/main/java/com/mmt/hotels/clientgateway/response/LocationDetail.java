package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class LocationDetail {
	
	private String id;
	private String name;
	private String type;
	private String countryId;
	private String countryName;
	private String displayName;  //This field is used by Shortstay funnel
	private String postalCode; // HTL-43657 postal code will come from hotstore along with state in response, against pincode we will send in request
	
	public LocationDetail () {
		
	}
	
	public LocationDetail (String id, String name, String type, String countryId, String countryName) {
		this.id = id;
		this.name = name;
		this.type = type;
		this.countryId = countryId;
		this.countryName = countryName;
	}

}
