package com.mmt.hotels.clientgateway.response.listing;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.request.Filter;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TreelsListingResponse extends ListingResponse{

    private boolean noMoreProducts;
    private ExploreMoreData exploreMoreData;
    private boolean allTreelsShown;
    private List<ListingProduct> products;
    private List<Filter> filterRemovedCriteria;
    private String lastProductId;
    private String expVariantKeys;
}
