package com.mmt.hotels.clientgateway.response.corporate;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DuplicateBookingDetails {
    private String travellerName;
    private String travellerEmailCommId;
    private String bookingId;
    private String hotelName;
    private String checkInDate;
    private String checkOutDate;
    private String roomName;
}
