package com.mmt.hotels.clientgateway.response.staticdetail;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PolicyRules implements Serializable {
    private static final long serialVersionUID = -1859173118634151746L;
    private String ageGroup;
    private Set<ExtraBedRules> extraBedTerms;

    public String getAgeGroup() {
        return ageGroup;
    }

    public void setAgeGroup(String ageGroup) {
        this.ageGroup = ageGroup;
    }

    public Set<ExtraBedRules> getExtraBedTerms() {
        return extraBedTerms;
    }

    public void setExtraBedTerms(Set<ExtraBedRules> extraBedTerms) {
        this.extraBedTerms = extraBedTerms;
    }
}