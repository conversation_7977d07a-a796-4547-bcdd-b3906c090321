package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PixelUrlConfig {
    private String currency;
    private String language;
    private String cityName;
    private String cost;
    private String hotelName;
    private String checkIn;
    private String checkOut;
    private int guestCount;
    private String userCountry;
    private String deviceId;
    private String journeyId;

    public PixelUrlConfig() {

    }

    public PixelUrlConfig(String currency, String language, String cityName, String cost, String hotelName, String checkIn, String checkOut, int guestCount, String userCountry, String deviceId, String journeyId) {
        this.currency = currency;
        this.language = language;
        this.cityName = cityName;
        this.cost = cost;
        this.hotelName = hotelName;
        this.checkIn = checkIn;
        this.checkOut = checkOut;
        this.guestCount = guestCount;
        this.userCountry = userCountry;
        this.deviceId = deviceId;
        this.journeyId = journeyId;
    }
}
