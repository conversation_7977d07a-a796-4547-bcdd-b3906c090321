package com.mmt.hotels.clientgateway.response.ugc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(
        ignoreUnknown = true
)
public class QuestionDataClient {
    private String questionId;
    private Integer indexId;
    private List<String> selectedOptionIds;
    private int ratingValue;
    private TextDetails textDetails;
    private List<AdditionalDetail> additionalDetails;
    private List<MediaDetails> mediaDetails;
    private boolean voiceInput;
}
