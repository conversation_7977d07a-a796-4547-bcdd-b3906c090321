package com.mmt.hotels.clientgateway.response.availrooms;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClmPersuasion {
    private String iconUrl;
    private String title;
    private String text;
    private String persuasionId;
    private String benefitId;
    private String benefitType;
    private String lob;
    private String page;
    private String benefitAmount;
    private int benefitExpiryInDays;
}
