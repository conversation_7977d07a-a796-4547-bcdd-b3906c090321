package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BookedInclusion {
	private String code;
    private IconType iconType;
    private String text;
    private String subText;
    private String segmentIdentifier;
    private String iconUrl;
    private String value;
    private String type;
    private String inclusionCode;
    //added category to segregate inclusion and reorder inclusions HTL-37514
    private String category;
    private boolean onOffer;
    private boolean bookable;
    private String descriptionText;
    //Added iUrl to show gocash coin icon on client
    @JsonProperty("iUrl")
    private String iUrl;
    @JsonIgnore
    private String leafCategory;
    @JsonIgnore
    private String amount;
    private boolean packageBenefit;
    private String textColor;
    private BenefitInclusionDetails inclusionsDetails;
    private boolean showOnSelectRoom;
    private String trailingCtaText;
    private TrailingCtaBottomSheet trailingCtaBottomSheet;
    private List<String> styleClasses;
}
