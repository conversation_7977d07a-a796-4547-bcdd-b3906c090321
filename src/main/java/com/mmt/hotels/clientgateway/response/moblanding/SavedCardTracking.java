package com.mmt.hotels.clientgateway.response.moblanding;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class SavedCardTracking {

    private Integer userSavedCardCount;

    private List<String> cardInfo;

    private Boolean offerApplicable;

}


/**
 * {
 *   "savedCardTracking": {
 *     "userSavedCardCount": 3,
 *     "cardInfo": [
 *       "HDFC_CC",
 *       "ICICI_DC",
 *       "HSBC_CC"
 *     ],
 *     "offerApplicable": true
 *   }
 * }
 */