package com.mmt.hotels.clientgateway.response.thankyou;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClmCashback {
    @JsonProperty("gocash_amt")
    private Double benefitAmount;
    private String title;
    @JsonProperty("img_url")
    private String imageUrl;
    @JsonProperty("max_retry")
    private int maxRetry;
    @JsonProperty("retry_in")
    private int retryIn;
    private String status;
    @JsonProperty("status_color")
    private String statusColor;
    @JsonProperty("status_msg")
    private String statusMsg;
    private String subtitle;
    @JsonProperty("status_textColor")
    private String statusTextColor;
    @JsonProperty("tg")
    private int tag;
    @JsonProperty("gd")
    private String goData;
}
