package com.mmt.hotels.clientgateway.response.ugc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(
        ignoreUnknown = true
)
@Data
public class QuestionDetails {
    private String id;
    private boolean jackpotQuestion;
    private int sectionId;
    private String dynamicQuestionType;
    private String iconUrl;
    private String questionDescription;
    private String questionSubtitle;
    private boolean isMandatory;
    private Integer userPage;
    private String answerHelpText;
    private String answerTitle;
    private OptionsInfo optionsInfo;
    private Validators validators;
    private List<AdditionalProperty> additionalProperties;
    private AnswerProvided answerProvided;
    private String parentQuestionId;
    private int pageId;
    private int previousPageId;
    private int level;
    private List<Level> levelconfig;
    private String tag;
    private String subText;
}
