package com.mmt.hotels.clientgateway.response.searchHotels;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.request.Filter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SectionFeature {
    private String iconUrl;
    private String text;
    private String iconType;
    private String actionTitle;
    private List<Filter> filterCriteria;
}
