package com.mmt.hotels.clientgateway.response.availrooms;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.response.PersuasionTimer;
import lombok.Data;


@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class HotelTag {


    public String icon;

    public String title;

    public String titleColor; //Additional node to incorporate BHF persuasions

    public String background;

    public String type;

    public String iconUrl;
    private String color;
    private PersuasionTimer timer;
    private String stickyText;
    private Boolean isSticky;
}

