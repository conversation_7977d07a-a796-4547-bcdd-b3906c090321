package com.mmt.hotels.clientgateway.response.rooms;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import lombok.Data;

import java.util.List;

/**
 * Represents information for a price graph, including its type, heading, description,
 * icon, and background gradient. This class is used in the client gateway response
 * for room-related data.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PriceGraphInfo {

	/**
	 * The type of the price graph (e.g., "bar", "line").
	 */
	private String type;

	/**
	 * The heading or title of the price graph.
	 */
	private String heading;

	/**
	 * A brief description of the price graph.
	 */
	private String description;

	/**
	 * The URL of the icon associated with the price graph.
	 */
	private String iconUrl;

	/**
	 * The background gradient settings for the price graph.
	 */
	private BGLinearGradient bgGradient;

	private String iconTitle;

	private String iconSubTitle;

	private List<AlternateDate> alternateDates;
}