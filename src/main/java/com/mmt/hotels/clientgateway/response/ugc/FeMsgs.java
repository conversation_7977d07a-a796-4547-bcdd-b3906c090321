package com.mmt.hotels.clientgateway.response.ugc;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FeMsgs {
    private ErrMsg1 errMsg1;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ErrMsg1 {
        private String code;
        private String title;
        private String msg;
        private Cta cta;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Cta {
        private String text;
        private String deeplink;
    }



}
