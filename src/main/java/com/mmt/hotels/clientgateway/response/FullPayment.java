package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FullPayment {
    private String fullPaymentText;
    private String fullPaymentSubText;
    private Integer finalPrice;
    private Integer totalAmount;
    private String payEntireBnplApplicableText;
    private String payEntireBnplApplicableTextV2;
    private String payEntireBnplNotApplicableText;
    private String payEntireBnplNotApplicableTextV2;
    private double bnplConvFees;
}
