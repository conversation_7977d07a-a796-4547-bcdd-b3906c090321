package com.mmt.hotels.clientgateway.response.streaks.balance;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class BookingDetails {
    @JsonProperty("max_bookings")
    int maxBookings;

    @JsonProperty("in_progress_bookings")

    int inProgressBookings;

    @JsonProperty("completed_bookings")
    int completedBookings;

    @JsonProperty("pending_label")
    String pendingLabel;

    @JsonProperty("hotel_details")

    ArrayList<HotelDetail> hotelDetails;
}
