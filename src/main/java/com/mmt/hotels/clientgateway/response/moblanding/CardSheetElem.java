package com.mmt.hotels.clientgateway.response.moblanding;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CardSheetElem {
    private String text;
    private String subText;
    private String iconUrl;
    private String purpose;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Style {
        private String borderColor;
    }

    private Style style;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SuccessInfo {
        private String text;
        private String subText;
        private String iconUrl;
        private Content content;

        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class Content {
            private String text;
            private List<ContentList> contentList;

            @Data
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class ContentList {
                private String text;
                private String iconUrl;
            }
        }
    }

    private SuccessInfo successInfo;
    private CardAction ctaAction;
    private List<GenericCardPayloadDataCG> infoList;
}

