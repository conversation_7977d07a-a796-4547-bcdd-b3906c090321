package com.mmt.hotels.clientgateway.response.rooms;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StayDetail {
    private Integer bed;
    private Integer roomCount;
    private Integer bedRoom;
    private Integer maxGuests;
    private Integer baseGuests;
    private Integer extraBeds;
    private LinkedHashMap<String,Integer> bedInfoMap;
    private String bedInfoText;
    private Integer maxCapacity;
    private Integer bathroom;
    private String sleepInfoText;
    private String additionalSleepInfoText;
    private String bedAndRoomAvailabilityText;
    private StayTypeInfo stayTypeInfo;
    private List<StayInfo> stayInfoList;
}
