package com.mmt.hotels.clientgateway.response.thankyou;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RtbPersuasionCard {
        private String title;
        private String text;
        private String iconUrl;
        private Boolean isPreApproved;
        private String approvedOn;
        private String expiry;
        private String iconDeepLink;
        private String iconText;
        private List<RtbCard> rtbCard;

}
