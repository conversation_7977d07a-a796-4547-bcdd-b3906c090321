package com.mmt.hotels.clientgateway.response.filter;


public enum FilterGroup {

    /**
     * Static Filters
     */
    STAR_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    LOCATION(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    PROPERTY_TYPE(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    MERGE_PROPERTY_TYPE(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    PROPERTY_CATEGORY(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    USER_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    USER_RATING_GI_BRAND(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    USER_RATING_MMT_BRAND(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    USER_RATING_AFFILIATE_BRAND(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    HOTEL_CATEGORY(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    BEST_POI_DISTANCE(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    MYBIZ_ALTACCO(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    PROPERTY_HOST(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    GLAZE_FILTERS(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    MMT_OFFERING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    ROOM_AMENITIES(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    ROOM_VIEWS(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    PREFERRED_RATES(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    HOTELS_SECTION(FilterConstants.FILTER_GROUP_TYPE_AND,false),

    AMENITIES(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    LANGUAGES(FilterConstants.FILTER_GROUP_TYPE_AND,false),

    IN_POLICY(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    DRIVING_DISTANCE_KM(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    DRIVING_DURATION_HR(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    CITY_CENTER_DRIVING_DISTANCE(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    POI_DRIVING_DISTANCE(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    STOREFRONTS(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    THEMES_BRAND(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    THEMES_LUXURY(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    HOUSE_RULES(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    BED(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    BEDROOM(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    PRIVATE_ROOM(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    CHAIN_INFO(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    PRIVATE_ROOM_V1(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    RUSH_DEALS(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    SPOTLIGHT(FilterConstants.FILTER_GROUP_TYPE_OR,false),

    UGC_ALL_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_SOLO_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_COUPLE_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_BUSINESS_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_FAMILY_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_GROUP_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_SEEK_TAGS(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_POOL_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_MEETING_ROOM_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_SPA_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_SALON_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_GYM_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_HOST_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_CARETAKER_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_SECURITY_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_HOSPITALITY_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_CLEANLINESS_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_CHILD_FRIENDLINESS_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_FOOD_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_FACILITIES_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_ROOM_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_VALUE_FOR_MONEY_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_LOCATION_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_SAFETY_TOPIC_RATING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_THREE_NIGHT(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_TWO_NIGHT(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_ONE_NIGHT(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_ZERO_NIGHT(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    ALT_ACCO_PROPERTY(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    VILLA_APARTMENT_HOUSES_MORE(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    TOP_CITIES(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    FOOD_ARRANGEMENTS(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    PILGRIMAGE(FilterConstants.FILTER_GROUP_TYPE_AND, false),
    GSTN_ASSURED(FilterConstants.FILTER_GROUP_TYPE_AND, false),

    /**
     * Dynamic Filters
     */
    HOTEL_PRICE(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    PAY_AT_HOTEL(FilterConstants.FILTER_GROUP_TYPE_OR,true),
    BOOK_NOW_PAY_LATER(FilterConstants.FILTER_GROUP_TYPE_OR,true),
    FREE_BREAKFAST(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    FREE_CANCELLATION_AVAIL(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    FREE_BREAKFAST_AVAIL(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    PAY_AT_HOTEL_AVAIL(FilterConstants.FILTER_GROUP_TYPE_OR,true),
    DEALS(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    TRAVEL_TYPE(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    UGC_CATEGORY(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    MOBIUS_AVAIL(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    MARKETING_COUPONS(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    EMI(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    MISCELLANEOUS(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    CUISINE_TYPE(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    BLACKDEALS(FilterConstants.FILTER_GROUP_TYPE_OR,true),
    POI(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    AREA(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    VIBE(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    PAY_LATER(FilterConstants.FILTER_GROUP_TYPE_OR,true),
    HOTEL_PRICE_BUCKET(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    STAYCATION_DEALS(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    HOTEL_PRICE_MANUAL(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    CTA_RATES(FilterConstants.FILTER_GROUP_TYPE_OR,true),
    MEAL_PLAN_AVAIL(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    BOOKING(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    BUDGET_HOTEL(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    LOSDEALS(FilterConstants.FILTER_GROUP_TYPE_OR,true),
    MYBIZ_SPECIAL_RATES(FilterConstants.FILTER_GROUP_TYPE_OR,true),
    VILLA_AND_APPT(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    PREMIUM_PROP(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    PURPOSE_BOOKING(com.mmt.hotels.filter.FilterConstants.FILTER_GROUP_TYPE_OR,false),
    PROPERTY_GROUP(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    PACKAGE_RATE(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    INSTANT_BOOKING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    TRANSIT_POIS(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    NON_PAY_AT_HOTEL(FilterConstants.FILTER_GROUP_TYPE_OR,true),
    DPT_COLLECTIONS(FilterConstants.FILTER_GROUP_TYPE_AND,true),
    DPT_PROP_COLLECTIONS(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    DRIVE_WALK_PROXIMITY_POI(FilterConstants.FILTER_GROUP_TYPE_OR,false),

    DRIVE_WALK_PROXIMITY_KEY_POI(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    DISTANCE_CITY_CENTRE(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    DISTANCE_INDIAN_RESTAURANT(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    DISTANCE_PUBLIC_TRANSPORT(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    DISTANCE_SHOPPING(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    DISTANCE_TOURIST_ATTRACTION(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    DISTANCE_RIVER(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    DISTANCE_LAKE(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    DISTANCE_BEACH(FilterConstants.FILTER_GROUP_TYPE_OR,false),


    /* GI Filters*/
    MEAL_PLAN(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    WOMEN_TRAVELLERS(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    GOSTAY(FilterConstants.FILTER_GROUP_TYPE_AND,true),
    DEAL_OF_THE_DAY(FilterConstants.FILTER_GROUP_TYPE_AND,true),
    BUSINESS_FRIENDLY(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    BEDROOM_COUNT(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    FLEXIBLE_CHECKIN(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    BUSEXCLUSIVE(FilterConstants.FILTER_GROUP_TYPE_AND,true),
    TRAINEXCLUSIVE(FilterConstants.FILTER_GROUP_TYPE_AND,true),

    PERFECT_TIMEOUT(FilterConstants.FILTER_GROUP_TYPE_AND,true),
    WELLNESS(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    SPIRITUAL(FilterConstants.FILTER_GROUP_TYPE_AND,false),
    EXACT_ROOM_RECOMMENDATION(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    HOTELS_USP(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    SALE_CAMPAIGN(FilterConstants.FILTER_GROUP_TYPE_OR,true),
    TAGS(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    NEARHOSPITALS(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    NEARCORPORATEHUBS(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    NEARBYSTAYCATIONS(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    INSTAWORTHY(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    FARMHOUSES(FilterConstants.FILTER_GROUP_TYPE_OR,false),
    GREAT_LOCATION(FilterConstants.FILTER_GROUP_TYPE_OR,false),

    SALE(com.mmt.hotels.filter.FilterConstants.FILTER_GROUP_TYPE_AND,false),
    PREMIUM(com.mmt.hotels.filter.FilterConstants.FILTER_GROUP_TYPE_AND, false),
    DEALSV2(com.mmt.hotels.filter.FilterConstants.FILTER_GROUP_TYPE_AND, false),
    COLLECTION(com.mmt.hotels.filter.FilterConstants.FILTER_GROUP_TYPE_AND, false),
    THEME(com.mmt.hotels.filter.FilterConstants.FILTER_GROUP_TYPE_AND, false),
    BUDGET(com.mmt.hotels.filter.FilterConstants.FILTER_GROUP_TYPE_AND, false),

    CATEGORY(com.mmt.hotels.filter.FilterConstants.FILTER_GROUP_TYPE_AND,false),
    CURATIONS(com.mmt.hotels.filter.FilterConstants.FILTER_GROUP_TYPE_AND, false),
    USER_BASED(com.mmt.hotels.filter.FilterConstants.FILTER_GROUP_TYPE_AND, false),
    USP(com.mmt.hotels.filter.FilterConstants.FILTER_GROUP_TYPE_AND, false),
    ROOM_TYPE(com.mmt.hotels.filter.FilterConstants.FILTER_GROUP_TYPE_OR, false),
    HOTELCLOUD(FilterConstants.FILTER_GROUP_TYPE_AND, true),
    ROOM_SIZE(FilterConstants.FILTER_GROUP_TYPE_AND, false),
    PACKAGE_DEAL(com.mmt.hotels.filter.FilterConstants.FILTER_GROUP_TYPE_OR, false),
    LOCATION_RATING_GI_BRAND(FilterConstants.FILTER_GROUP_TYPE_AND, false);
    private int filterType;
    private boolean timeBound;

    FilterGroup(int filterType,boolean timeBound) {
        this.filterType = filterType;
        this.timeBound = timeBound;
    }

    public int getFilterType() {
        return filterType;
    }

    public boolean getTimeBound() {
        return timeBound;
    }

    public static int getFilterGroupCount() {
        return FilterGroup.values().length;
    }

    public static FilterGroup getFilterGroupFromFilterName(String filterName) {
        for (FilterGroup filterGroup : FilterGroup.values()) {
            if (filterGroup.name().equalsIgnoreCase(filterName))
                return filterGroup;
        }
        return null;
    }
}
