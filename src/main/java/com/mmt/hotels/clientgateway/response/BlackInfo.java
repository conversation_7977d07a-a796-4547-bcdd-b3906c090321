package com.mmt.hotels.clientgateway.response;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient;
import com.mmt.hotels.model.response.pricing.Inclusion;

import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BlackInfo {
    private String headerIcon;
    private String tierName;
    private String tierNumber;
    private String iconUrl;
    private String iconUrlV2;
    private String benefitsTitle;
    private List<String> lineBgColor;
    private String currencyIcon;
    private String msg;
    private String ctaText;
    private String ctaUrl;
    private String borderColor;
    private List<Inclusion> inclusionsList;
    private Long campaignEndTime;
    private String title;
    private String cardId;
    private String bgImageUrl;
    private boolean mmtSelectPrivilige;
    private boolean blackUser;
    private String separator;
    private BorderGradient borderGradient;
    private String titleImageUrl;
}
