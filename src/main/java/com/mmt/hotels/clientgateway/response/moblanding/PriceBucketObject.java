package com.mmt.hotels.clientgateway.response.moblanding;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.EqualsAndHashCode;
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@EqualsAndHashCode(callSuper = true)
public class PriceBucketObject extends AbstractFilterType  {

    private Double minPrice;
    private Double maxPrice;

    public PriceBucketObject(){
        //Empty
    }

    //TODO: Discuss
    private PriceBucketObject(Builder builder) {
        this.minPrice = builder.minPrice;
        this.maxPrice = builder.maxPrice;
        this.count = builder.count;
    }

    public static final class Builder {
        private Double minPrice;
        private Double maxPrice;
        private int count;

        public Builder() {
        }

        public Builder minPrice(Double val) {
            minPrice = val;
            return this;
        }

        public Builder maxPrice(Double val) {
            maxPrice = val;
            return this;
        }

        public Builder maxPrice(int val) {
            count = val;
            return this;
        }

        public PriceBucketObject build() {
            return new PriceBucketObject(this);
        }
    }


}
