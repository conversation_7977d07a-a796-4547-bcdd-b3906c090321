package com.mmt.hotels.clientgateway.response.thankyou;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.request.payment.InsuranceInfo;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class BookingDetails {
    private BookingStatus status;
    private String bookingId;
    private String pnr;
    private BookerInfo bookerInfo;
    private List<InsuranceInfo> insuranceInfo;
    private String checkInDate;
    private String checkOutDate;
    private String checkInTime;
    private String checkOutTime;
    private String checkInPolicyDesc;
    private String paymentMode;
    private boolean donated;
    private boolean isBlackRegSuccess;
    private boolean bnplBooking;
    private boolean bookingWithInsurance;
    private boolean pahBooking;
    private boolean doubleBlackValidated;
    private Boolean requestToBook;
    private Boolean rtbPreApproved;
    private Boolean rtbAutoCharge;
    private Integer nights;
    private SuccessBookingCardInfo successBookingCardInfo;
    private PendingBookingCardInfo pendingBookingCardInfo;
    private FailedBookingCardInfo failedBookingCardInfo;
    private CompletePaymentCard completePaymentCard;
    private long bookingDate;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private boolean showTimeRangeUi;
}
