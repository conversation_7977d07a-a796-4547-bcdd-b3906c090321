package com.mmt.hotels.clientgateway.response.thankyou;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicy;
import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.corporate.CorpApprovalInfo;
import com.mmt.hotels.clientgateway.response.corporate.CorpRateTags;
import com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline;
import lombok.Data;

import java.util.LinkedList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class BookedRatePlan {
    private String roomName;
    private String ratePlanName;
    private boolean basePlan;
    private String highlightImage;
    private BookedOccupancy occupancy;
    private Integer roomCount;
    private BookedCancellationPolicy cancellationPolicy;
    private CancellationPolicyTimeline cancellationPolicyTimeline;
    private List<BookedInclusion> inclusions;
    private CorpApprovalInfo corpApprovalInfo;
    private List<CorpRateTags> corpRateTags;
    private LinkedList<String> images;
    private boolean packageRateAvailable;
    private String type;
}
