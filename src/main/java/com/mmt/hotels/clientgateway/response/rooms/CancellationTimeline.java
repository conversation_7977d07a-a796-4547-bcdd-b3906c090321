package com.mmt.hotels.clientgateway.response.rooms;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class CancellationTimeline {
    private String startDate;
    private String endDate;
    private String endDateTime;
    private String text;
    private boolean refundable;
    private FlexiCancellationDetails flexiCancellationDetails;
}
