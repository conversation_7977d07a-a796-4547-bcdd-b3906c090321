package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class EMIDetail {
	
	private String type;
    private int tenure;
    private Double totalInterest;
    private String bankName;
    private String bankId;
    private Double interestRate;
    private Double amount;
    private Double totalCost;
    private String payOption;

    private Boolean taxIncluded;

    private String termType;

    private String interestType;
}
