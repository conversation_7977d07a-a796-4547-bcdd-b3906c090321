package com.mmt.hotels.clientgateway.response.gi.taggedmedia;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
@JsonInclude(content = JsonInclude.Include.NON_NULL)
@Data
public class ProcessedVideos {
    @JsonProperty("mp4_format")
    private FormatUrl mp4Format;
    @JsonProperty("webm_format")
    private FormatUrl webmFormat;
    @JsonProperty("mp4_thumbnail")
    private FormatUrl mp4Thumbnail;
    @JsonProperty("webm_thumbnail")
    private FormatUrl webmThumbnail;
    private String snapshotUrl;
}
