package com.mmt.hotels.clientgateway.response.staticdetail;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.gi.hotels.model.response.staticdata.*;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.availrooms.DayUseInfo;
import com.mmt.hotels.clientgateway.response.availrooms.HotelTag;
import com.mmt.hotels.clientgateway.response.dayuse.DayUseDetails;
import com.mmt.hotels.clientgateway.response.moblanding.SupportDetails;
import com.mmt.hotels.clientgateway.response.rooms.SpaceData;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.CalendarCriteria;
import com.mmt.hotels.clientgateway.response.streaks.userinfo.StreaksUserInfoResponse;
import com.mmt.hotels.model.response.searchwrapper.CategoryDetails;
import com.mmt.hotels.model.response.staticdata.AmenitiesList;
import com.mmt.hotels.model.response.staticdata.ChatbotInfo;
import com.mmt.hotels.model.response.staticdata.FlexibleCheckinInfo;
import com.mmt.hotels.model.response.staticdata.PremiumUsp;
import com.mmt.hotels.model.response.staticdata.StaffInfo;
import com.mmt.model.UGCRatingData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class HotelResult {
	private FoodDining foodAndDining; // GI specific
	private String mapImageUrl; // GI specific
	private Map<String, RoomReviews> roomReviews; // GI specific
	private String cachedAt; // GI specific
	private Amenities amenitiesGI; // GI specific
	private TopLeftPersuasion coupleFriendlyPersuasion; // GI specific
	private boolean supressHouseRules; // GI specific
	private HotelVideos hotelVideos;
	private Address address;
	private String checkinTime;
	private String checkoutTime;
	private String checkinTimeRange;
	private String checkoutTimeRange;
	private String id;
	private int bedroomCount;
	private String giHotelId;
	private String ingoId;
	private String name;
	private boolean maskedPropertyName;
	private boolean showCallToBook;
	private SpaceData privateSpacesV2;
	private SpaceData sharedSpacesV2;
	private Set<SpaceData> giPrivateSpacesV2;
	private Set<SpaceData> giSharedSpacesV2;
	private String propertyLayoutTitleText;
	private Boolean isABO;
	private String titleIcon;
	private TitleData titleData;
	private LocationDetail locationDetail;
	private Integer starRating;
	private String starRatingType;
	private Boolean highSellingAltAcco;
	private List<String> categories;
	private String bedInfoText; //BedInfoText added to summarize the bed types and count for property Layout
	private String email;
	private CategoryTag categoryTag;
	private Integer popularType;
	private ReportCardPersuasion reportCardPersuasion;
	private String mobile;
	private String pinCode;
	private String propertyType;
	private String propertyLabel;
	private String hotelId;
	private Boolean freeWifi;
	private String hotelIcon;
	private String cityName;
	private String primaryArea;
	private List<String> addressLines;
	private String shortDesc;
	private String shortDescSeo;
	private String longDesc;
	private boolean entireProperty;
	private boolean secrecyOption;
	private HotelReviewIncognito incognitoModel;
	private String entirePropertyText;
	private String sellableType;
	private HouseRules houseRules;
	private String stayType;
	private Boolean altAcco;
	private Double lat;
	private Double lng;
	private Double userRating;
	private String countryCode;
	private String countryName;
	private String listingType;
	private String guestRoomKey;
	private String guestRoomValue;
	private String searchType;
	private String checkinDate;
	private String checkoutDate;
	private HostInfo hostInfo;
	private StaffInfo staffInfo;
	private UGCRatingData amenitiesRatingData;
	private List<SelectRoomAmenities> amenities;
	private String highlightedAmenitiesTag;
	private List<String> highlightedAmenities;
	private List<HighlightedAmenity> highlightedAmenitiesV2;
	private List<String> longStayAmenities;
	private List<String> locationPersuasion;
	private String locationId;
	private String locationType;
	private String funnelSource;
	private Integer totalGuestCount;
	private String sharingUrl;
	private String detailDeeplinkUrl;
	private String listingDeeplinkUrl;  // link to show the similar hotels in case the hotel is available but sold out
	private String propertyUnavailableImg;  //Image required by client in case the hotel is available but sold out
	private String contextType;
	private String context;
	private Map<String, CategoryDetails> categoryDetails;
	private Map<String, HotelTag> hotelTags;
	private boolean alternateDatesAvailable;
	private LinkedHashMap<String,Map<String, HotelCategoryData>> applicableHotelCategoryData;
	private LinkedHashMap<String, HotelCategoryDataWeb> applicableHotelCategoryDataWeb;
	private String mmtHotelCategory;
	private String mmtHotelText;
	private String luxeIcon;
	private List<AmenitiesList> signatureAmenities;
	private Map<String, String> cardTitleMap;
	private String heroImage;
	private String heroVideoUrl;
	private FaqData faqData;
	private boolean groupBookingQueryEnabled;
	private String groupBookingWebUrl;
	private DayUseInfo dayUseInfo;
	private HouseRulesV2 houseRulesV2;
	private HouseRulesV2 foodDining; // this node helps the clients to Identify the new FoodAndDining data that will be displayed exactly like houseRulesV2
	private String quickBookSubTitle;
	private String supplierType;
	@ApiModelProperty(notes = "This flag indicates if hotel is group booking hotel")
	private Boolean groupBookingHotel;
	@ApiModelProperty(notes = "This flag indicates if group booking price is available for the hotel")
	private Boolean groupBookingPrice;
	@ApiModelProperty(notes = "This flag indicates if price has to be hidden on front-end by clients")
	private Boolean maskedPrice;
	private boolean isWishListed;
	private String roomText;
	private DayUseDetails dayUseDetails;
	@ApiModelProperty(notes = "calendarCriteria node for showing calendar on details page for a property")
	private CalendarCriteria calendarCriteria;

	//This field will be used to show icon based on hotelCategory (e.g. in this case MMT ValueStays)
	private String categoryIcon;

	private List<GovtPolicies> govtPolicies; //This field will carry list of gov Policies receive from HES static details

	private String categoryUspDetailsText; //If a hotel has categoryUsp text coming from HES, this node will carry the disclaimer text for details page

	/* HTL-40907: Flag to indicate if any of the rate plan is negotiated rate plan.
		Negotiated rates are the one-on-one rates that are directly negotiated between the corporate/organization and the hotel.
	 */
	private boolean specialFare;
	private boolean goStay;
	private String propertyRulesTitle;
	@JsonInclude(JsonInclude.Include.NON_DEFAULT)
	private boolean showTimeRangeUi;
	private boolean checkInTimeInRange;
	private boolean checkOutTimeInRange;
	private StreaksUserInfoResponse streaksUserInfoResponse; //Used in Thank you page for GI
	private Boolean isRTB;
	private Boolean isABSO;
	private Boolean isMLOS;
	private com.mmt.hotels.model.response.staticdata.HostInfoV2 hostInfoV2;
	private String childOccupancyMsg;
	private HomeStayAwardDetails homeStayAwardDetails;
	private String uspHotelDesc; // This represents usp persuasion text powered by chatGPT
	private Map<String, List<String>> flexibleCheckin;
	private FlexibleCheckinInfo flexibleCheckinInfo;
    private String hotelCategories;
	private SupportDetails supportDetails;
	private PropertyChainCG propertyChain;
	private PropertyHighlightCG propertyHighlights;
	public StreaksUserInfoResponse getStreaksUserInfoResponse() {
		return streaksUserInfoResponse;
	}
	private PremiumUsp premiumUsp;
	private BottomSheet requestCallbackData;
	private String packageTagUrl;
	private StreetViewInfo streetViewInfo;
	private boolean streetViewDataAvailable;
	private ChatbotInfo chatbotInfo;

	public StreetViewInfo getStreetViewInfo() { return streetViewInfo; }

	public void setStreetViewInfo(StreetViewInfo streetViewInfo) { this.streetViewInfo = streetViewInfo; }

	public void setStreaksUserInfoResponse(StreaksUserInfoResponse streaksUserInfoResponse) {
		this.streaksUserInfoResponse = streaksUserInfoResponse;
	}

	public boolean isWishListed() {
		return isWishListed;
	}

	@JsonSetter("isWishListed")
	public void setWishListed(boolean wishListed) {
		isWishListed = wishListed;
	}

	public String getPropertyRulesTitle() {
		return propertyRulesTitle;
	}

	public void setPropertyRulesTitle(String propertyRulesTitle) {
		this.propertyRulesTitle = propertyRulesTitle;
	}


	public boolean isShowTimeRangeUi() {
		return showTimeRangeUi;
	}

	public void setShowTimeRangeUi(boolean showTimeRangeUi) {
		this.showTimeRangeUi = showTimeRangeUi;
	}
}