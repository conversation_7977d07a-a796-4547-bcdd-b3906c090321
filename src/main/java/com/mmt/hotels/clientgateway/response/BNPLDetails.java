package com.mmt.hotels.clientgateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BNPLDetails {

	private boolean bnplApplicable;

	private String bnplPersuasionMsg;

	private String bnplPolicyText;

	@JsonProperty("bnplText")
	private String bnplNewVariantText;

	@JsonProperty("bnplSubText")
	private String bnplNewVariantSubText;

	@JsonProperty("bnplSubTextV2")
	private String bnplNewVariantSubTextV2;

	private String bnplVariant;

    private String bnplDisabledMsg;

	private PriceFooter priceFooter;

	private Integer finalPrice;

	private Boolean loggedOutBnplWebExp;

	private double bnplConvFees;

	private List<PricingDetails> details;

	private Integer totalAmount;

	public Boolean getLoggedOutBnplWebExp() {
		return loggedOutBnplWebExp;
	}

	public void setLoggedOutBnplWebExp(Boolean loggedOutBnplWebExp) {
		this.loggedOutBnplWebExp = loggedOutBnplWebExp;
	}

	public boolean isBnplApplicable() {
		return bnplApplicable;
	}

	public void setBnplApplicable(boolean bnplApplicable) {
		this.bnplApplicable = bnplApplicable;
	}

	public String getBnplPersuasionMsg() {
		return bnplPersuasionMsg;
	}

	public void setBnplPersuasionMsg(String bnplPersuasionMsg) {
		this.bnplPersuasionMsg = bnplPersuasionMsg;
	}

	public String getBnplPolicyText() {
		return bnplPolicyText;
	}

	public void setBnplPolicyText(String bnplPolicyText) {
		this.bnplPolicyText = bnplPolicyText;
	}

	public String getBnplNewVariantText() {
		return bnplNewVariantText;
	}

	public void setBnplNewVariantText(String bnplNewVariantText) {
		this.bnplNewVariantText = bnplNewVariantText;
	}

	public String getBnplNewVariantSubText() {
		return bnplNewVariantSubText;
	}

	public void setBnplNewVariantSubText(String bnplNewVariantSubText) {
		this.bnplNewVariantSubText = bnplNewVariantSubText;
	}

	public String getBnplVariant() {
		return bnplVariant;
	}

	public void setBnplVariant(String bnplVariant) {
		this.bnplVariant = bnplVariant;
	}

    public String getBnplDisabledMsg() {
        return bnplDisabledMsg;
    }

    public void setBnplDisabledMsg(String bnplDisabledMsg) {
        this.bnplDisabledMsg = bnplDisabledMsg;
    }

	public PriceFooter getPriceFooter() {
		return priceFooter;
	}

	public void setPriceFooter(PriceFooter priceFooter) {
		this.priceFooter = priceFooter;
	}

	public Integer getFinalPrice() {
		return finalPrice;
	}

	public void setFinalPrice(Integer finalPrice) {
		this.finalPrice = finalPrice;
	}

	public double getBnplConvFees() {
		return bnplConvFees;
	}

	public void setBnplConvFees(double bnplConvFees) {
		this.bnplConvFees = bnplConvFees;
	}

	public List<PricingDetails> getDetails() {
		return details;
	}

	public void setDetails(List<PricingDetails> details) {
		this.details = details;
	}

	public String getBnplNewVariantSubTextV2() {
		return bnplNewVariantSubTextV2;
	}

	public void setBnplNewVariantSubTextV2(String bnplNewVariantSubTextV2) {
		this.bnplNewVariantSubTextV2 = bnplNewVariantSubTextV2;
	}

	public Integer getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(Integer totalAmount) {
		this.totalAmount = totalAmount;
	}
}
