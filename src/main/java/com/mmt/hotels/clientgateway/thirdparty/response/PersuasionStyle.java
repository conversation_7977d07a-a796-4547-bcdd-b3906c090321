package com.mmt.hotels.clientgateway.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mmt.hotels.model.persuasion.response.BgGradient;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PersuasionStyle {
    List<String> styleClasses;
    private String textColor;
    private String fontType;
    private String fontSize;
    private String borderColor;
    private String iconHeight;
    private String iconWidth;
    private String imageHeight;
    private String imageWidth;
    private String borderSize;
    private String cornerRadii;
    private String imageUrl;
    private BgGradient bgGradient;
}
