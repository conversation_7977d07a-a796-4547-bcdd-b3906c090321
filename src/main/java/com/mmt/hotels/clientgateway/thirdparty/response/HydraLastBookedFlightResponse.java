package com.mmt.hotels.clientgateway.thirdparty.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class HydraLastBookedFlightResponse {
	
	@JsonProperty("request_id")
	private String requestId;

	@JsonProperty("code")
	private int code;

	@JsonProperty("data")
	private Map<String, Map<String, HydraLastBookedResponseEntity>> data;

	@JsonProperty("error")
	private Map<String, String> error;

	@JsonProperty("message")
	private String message;

}
